---
type: "always_apply"
---

# Intelligent Rehabilitation System - AI Development Rules

The number of lines in each code file must not exceed 350
Each time you run a backend file, be sure to first enter the virtual environment using venv/Script/activate, and then run the python file using windows commands
# Intelligent Rehabilitation System - AI Development Rules

## Project Overview

- **System Type**: Real-time intelligent rehabilitation system with pose analysis
- **Backend Stack**: Python Flask + SocketIO + ZeroMQ + OpenCV + RTMPose
- **Frontend Stack**: Vue 3 + Vite + Pinia + Element Plus + TailwindCSS + Socket.IO Client
- **Architecture**: Full-stack modular system with state machine, real-time communication, computer vision
- **Core Function**: Rehabilitation exercise recognition, scoring, and guidance
- **File Size Limit**: **CRITICAL** - Maximum 350 lines per code file
- **Communication**: Real-time bidirectional Socket.IO between frontend and backend

## Critical Architecture Rules

### State Machine Management
- **NEVER** modify system states without updating `backend/models/system_states.py`
- **ALWAYS** follow state transition rules defined in `StateTransitionRules.VALID_TRANSITIONS`
- **REQUIRED** state flow: IDEL → WAITING → USER_LOGIN → ACTION_PREPARATION → ACTION_TRAINING → ACTION_REST → ACTION_COMPLETED
- **FORBIDDEN**: Direct state jumps that bypass validation rules
- **MANDATORY**: Use `StateValidator.validate_transition_event()` before state changes
- **CRITICAL**: Use `state_manager.transition_to(event, **kwargs)` for all state changes

### Data Model Constraints
- **CRITICAL**: RTMPose requires exactly 133 keypoints in `[[x,y,confidence]]` format
- **MANDATORY**: Use `DataValidator.validate_keypoint()` for all pose data
- **REQUIRED**: All ZMQ data must pass `DataValidator.validate_zmq_detect_data()`
- **FORBIDDEN**: Modifying keypoint count or format without updating entire pipeline
- **CRITICAL**: Use `ZMQDetectData` and `ZMQCameraFrame` structures for all ZMQ communication

## Module Structure Rules

### Backend Directory Structure
```
backend/
├── models/          # Data structures, validation, state definitions
├── algorithms/      # Pose analysis, action recognition, scoring
├── services/        # Business logic, communication, streaming
├── data/           # User data, task templates
├── config/         # System configuration
├── utils/          # Utilities and validators
└── tests/          # Unit tests
```

### File Modification Dependencies
- **models/data_models.py** changes → Update all algorithm files + services
- **models/system_states.py** changes → Update all service state handlers
- **algorithms/*.py** changes → Update corresponding service business logic
- **data/tasks_template.json** changes → Update business/task services
- **config/system_config.json** changes → Update utils/config_manager.py

## Communication Protocol Rules

### WebSocket Messages
- **MANDATORY**: Use `MessageType` enum from `data_models.py`
- **REQUIRED**: Include `timestamp`, `message_type`, `data` in all messages
- **FORBIDDEN**: Custom message types not defined in enum
- **CRITICAL**: Validate all messages with `DataValidator.validate_websocket_message()`
- **REQUIRED**: Use `WebSocketMessage` dataclass for all WebSocket communications

### ZeroMQ Integration
- **REQUIRED**: Use `ZMQDetectData` and `ZMQCameraFrame` structures
- **MANDATORY**: Validate pose data before processing
- **FORBIDDEN**: Direct frame processing without validation
- **CRITICAL**: Use ports 6070 (detect) and 6080 (camera) as defined in config

## Rehabilitation Action Rules

### Supported Actions
- **ONLY**: `shoulder_touch`, `arm_raise`, `finger_touch`, `palm_flip`
- **FORBIDDEN**: Adding new actions without updating entire recognition pipeline
- **REQUIRED**: Each action needs scoring algorithm in `algorithms/scoring.py`
- **MANDATORY**: Update `ActionType` enum when adding actions

### Action Implementation Requirements
- **MANDATORY**: Define `required_keypoints` for each action in `ActionInfo`
- **REQUIRED**: Implement scoring logic with 0-100 scale in `algorithms/scoring.py`
- **CRITICAL**: Update action recognition in `algorithms/action_recognition.py`

## Service Layer Rules

### Business Services (`services/business/`)
- **RESPONSIBILITY**: User management, task logic, session handling, state management
- **FORBIDDEN**: Direct pose processing in business layer
- **REQUIRED**: Use state handlers in `state_handlers/` directory
- **MANDATORY**: All business logic must go through `system_coordinator.py`

### Communication Services (`services/communication/`)
- **RESPONSIBILITY**: WebSocket and ZMQ message handling
- **REQUIRED**: Message validation before forwarding
- **FORBIDDEN**: Business logic in communication layer
- **MANDATORY**: Use `websocket/` and `zmq/` subdirectories

### Streaming Services (`services/streaming/`)
- **RESPONSIBILITY**: Video stream processing and distribution
- **FORBIDDEN**: Business logic in streaming layer
- **REQUIRED**: Use `mjpeg_stream.py` for video streaming

## Coding Standards

### Import Rules
- **MANDATORY**: Use relative imports within backend modules
- **REQUIRED**: Import `SystemState` from `models.system_states`
- **FORBIDDEN**: Circular imports between models and services
- **CRITICAL**: Import order: standard library → third-party → local modules

### Error Handling
- **REQUIRED**: Use try-catch for all pose data parsing
- **MANDATORY**: Return `None` for invalid data, never raise exceptions in validators
- **CRITICAL**: Log all state transition failures
- **REQUIRED**: Use logger from `utils.logger_setup`

### Data Validation
- **ALWAYS**: Validate input data before processing
- **NEVER**: Trust external data without validation
- **REQUIRED**: Use Pydantic models for complex data structures
- **MANDATORY**: Use validators from `utils.data_validator`

## Configuration Management

### System Configuration
- **REQUIRED**: Use `config/system_config.json` for all configuration
- **MANDATORY**: Access config through `utils.config_manager`
- **FORBIDDEN**: Hardcoded values in code
- **CRITICAL**: Update config_manager when adding new config sections

### Environment Setup
- **CRITICAL**: Always activate virtual environment: `backend/venv/Scripts/activate`
- **REQUIRED**: Install dependencies from `requirements.txt`
- **FORBIDDEN**: Global package installation
- **MANDATORY**: Use Windows commands for backend execution

## Testing Requirements

### Unit Test Rules
- **MANDATORY**: Test all validators with valid/invalid data
- **REQUIRED**: Test state transitions with all valid events
- **CRITICAL**: Mock ZMQ and WebSocket connections in tests
- **REQUIRED**: Use `tests/` directory for all test files

### Integration Test Rules
- **REQUIRED**: Test complete action recognition pipeline
- **MANDATORY**: Validate end-to-end message flow
- **CRITICAL**: Test state machine transitions

## AI Decision Guidelines

### When Modifying Pose Analysis
1. **FIRST**: Check if change affects 133-keypoint requirement
2. **THEN**: Update validation logic in `utils/data_validator.py`
3. **FINALLY**: Test with all supported actions

### When Adding New Features
1. **FIRST**: Determine required state transitions
2. **THEN**: Update state machine rules in `models/system_states.py`
3. **FINALLY**: Implement business logic in appropriate service

### When Debugging Issues
1. **FIRST**: Check state transition validity
2. **THEN**: Validate data format compliance
3. **FINALLY**: Verify message type consistency

## Strict Prohibitions

### Architecture Violations
- **NEVER** bypass state machine validation
- **NEVER** modify keypoint format without full system update
- **NEVER** add direct database access in algorithm layer
- **NEVER** exceed 350 lines per file

### Data Handling Violations
- **NEVER** process unvalidated pose data
- **NEVER** modify core data structures without dependency updates
- **NEVER** ignore validation failures

### Communication Violations
- **NEVER** send custom WebSocket message types
- **NEVER** process ZMQ data without structure validation
- **NEVER** bypass message validation layers

## Emergency Procedures

### System Reset Protocol
- **TRIGGER**: Use `StateTransitionEvent.SYSTEM_RESET`
- **EFFECT**: Return to `SystemState.WAITING`
- **CLEANUP**: Clear user session and action data

### Error Recovery
- **VALIDATION_FAILURE**: Log error, return to previous valid state
- **COMMUNICATION_ERROR**: Attempt reconnection, notify frontend
- **POSE_DETECTION_ERROR**: Continue with last valid pose data

## File Organization Rules

### Models Directory
- **data_models.py**: All data structures and message types
- **system_states.py**: State machine definitions and transition rules
- **constants.py**: System constants and configurations

### Services Directory Structure
- **business/**: User management, task logic, state handling
- **communication/**: WebSocket and ZMQ handlers
- **streaming/**: Video stream processing

### Algorithms Directory
- **action_recognition.py**: Action detection and classification
- **pose_analyzer.py**: Pose analysis and keypoint processing
- **scoring.py**: Action scoring algorithms

---

## Frontend Architecture Rules

### Vue 3 Component Structure
- **MANDATORY**: Use Vue 3 Composition API with `<script setup>` syntax
- **REQUIRED**: Follow single-file component structure: `<template>`, `<script setup>`, `<style scoped>`
- **FORBIDDEN**: Options API usage in new components
- **CRITICAL**: Use `ref()` for reactive primitives, `reactive()` for objects
- **REQUIRED**: Import utilities from `@/utils/` using absolute paths

### Frontend Directory Structure
```
frontend/src/
├── components/      # Reusable UI components
│   ├── common/      # Generic components (buttons, inputs, etc.)
│   └── visualization/ # Pose visualization components
├── views/          # Page-level components (routes)
├── stores/         # Pinia state management
├── services/       # API and WebSocket services
├── utils/          # Utility functions and helpers
├── router/         # Vue Router configuration
└── style.css      # Global styles
```

### Pinia Store Management
- **MANDATORY**: Use Pinia with Composition API syntax (`defineStore` with setup function)
- **REQUIRED**: Store structure: `useSystemStore`, `useUserStore`, `useTrainingStore`
- **CRITICAL**: State synchronization with backend via Socket.IO messages
- **FORBIDDEN**: Direct state mutation outside store actions
- **REQUIRED**: Use computed properties for derived state

### Component Communication Rules
- **MANDATORY**: Use props for parent-to-child communication
- **REQUIRED**: Use emits for child-to-parent communication
- **FORBIDDEN**: Direct store access in deeply nested components
- **CRITICAL**: Use provide/inject for cross-component utilities
- **REQUIRED**: Validate props with TypeScript-style definitions

### Element Plus Integration
- **MANDATORY**: Use Element Plus components for UI consistency
- **REQUIRED**: Import components on-demand: `import { ElButton } from 'element-plus'`
- **FORBIDDEN**: Global component registration
- **CRITICAL**: Use Element Plus theme variables for consistent styling
- **REQUIRED**: Follow Element Plus naming conventions for custom components

### TailwindCSS Styling Rules
- **MANDATORY**: Use TailwindCSS utility classes for styling
- **FORBIDDEN**: Inline styles or custom CSS classes unless absolutely necessary
- **REQUIRED**: Use responsive design classes: `sm:`, `md:`, `lg:`, `xl:`
- **CRITICAL**: Maintain consistent spacing using Tailwind spacing scale
- **REQUIRED**: Use Tailwind color palette, avoid custom colors

### Socket.IO Frontend Integration
- **MANDATORY**: Use `@/services/websocket.js` for all Socket.IO communication
- **REQUIRED**: Validate all incoming messages with `@/utils/messageValidator.js`
- **FORBIDDEN**: Direct socket.io usage outside websocket service
- **CRITICAL**: Handle connection states: connecting, connected, disconnected, error
- **REQUIRED**: Implement message queuing for offline scenarios

## Cross-System Communication Rules

### Frontend-Backend Message Protocol
- **MANDATORY**: Use Socket.IO for real-time bidirectional communication
- **REQUIRED**: Message format: `{ timestamp, message_type, data }`
- **CRITICAL**: Frontend must validate message types against supported list
- **FORBIDDEN**: Custom message types not defined in backend `MessageType` enum
- **REQUIRED**: Handle message validation failures gracefully

### State Synchronization
- **CRITICAL**: Frontend system state must mirror backend state machine
- **MANDATORY**: Update frontend state only via Socket.IO messages
- **FORBIDDEN**: Frontend state changes without backend confirmation
- **REQUIRED**: Handle state transition failures with user feedback
- **CRITICAL**: Implement state recovery mechanisms for connection loss

### Real-time Data Flow
- **REQUIRED**: Pose data flows: Backend ZMQ → Backend Processing → Frontend via Socket.IO
- **MANDATORY**: Video stream: Backend MJPEG → Frontend display
- **FORBIDDEN**: Direct frontend access to ZMQ or camera streams
- **CRITICAL**: Handle data validation failures without breaking UI

## Frontend Development Standards

### File Naming Conventions
- **MANDATORY**: Vue components: PascalCase (e.g., `TrainingView.vue`)
- **REQUIRED**: JavaScript files: camelCase (e.g., `websocketService.js`)
- **REQUIRED**: Store files: camelCase with descriptive names (e.g., `system.js`)
- **FORBIDDEN**: kebab-case for Vue components
- **CRITICAL**: Use descriptive names that indicate component purpose

### Import and Export Rules
- **MANDATORY**: Use ES6 import/export syntax
- **REQUIRED**: Absolute imports with `@/` alias for src directory
- **FORBIDDEN**: Relative imports beyond one level (`../`)
- **CRITICAL**: Import order: Vue/framework → third-party → local utilities → local components
- **REQUIRED**: Named exports for utilities, default exports for components

### Error Handling Frontend
- **REQUIRED**: Use try-catch for all async operations
- **MANDATORY**: Display user-friendly error messages via Element Plus notifications
- **FORBIDDEN**: Console.error without user notification for critical errors
- **CRITICAL**: Implement error boundaries for component-level error handling
- **REQUIRED**: Log errors to frontend error tracking system

### Performance Optimization
- **MANDATORY**: Use `v-memo` for expensive list rendering
- **REQUIRED**: Implement lazy loading for route components
- **FORBIDDEN**: Unnecessary reactive references for static data
- **CRITICAL**: Debounce user input for real-time features
- **REQUIRED**: Use `shallowRef` for large objects that don't need deep reactivity

## Development Workflow Rules

### Environment Setup
- **CRITICAL**: Frontend development server: `npm run dev` (Vite)
- **REQUIRED**: Backend virtual environment: `backend/venv/Scripts/activate`
- **MANDATORY**: Start backend before frontend for Socket.IO connection
- **FORBIDDEN**: Production builds without environment variable validation

### Package Management
- **MANDATORY**: Use npm for frontend dependency management
- **REQUIRED**: Use pip with requirements.txt for backend dependencies
- **FORBIDDEN**: Mixing package managers (no yarn with npm)
- **CRITICAL**: Lock file consistency: commit package-lock.json and requirements.txt

### Build and Deployment
- **REQUIRED**: Frontend build: `npm run build` → `dist/` directory
- **MANDATORY**: Backend deployment: activate venv → install requirements → run app
- **FORBIDDEN**: Direct file editing in production
- **CRITICAL**: Environment-specific configuration files

## File Dependency Rules (Cross-System)

### Critical Synchronization Points
- **backend/models/data_models.py** changes → Update `frontend/src/utils/messageValidator.js`
- **backend/models/system_states.py** changes → Update `frontend/src/stores/system.js`
- **backend/services/communication/websocket/** changes → Update `frontend/src/services/websocket.js`
- **backend/config/system_config.json** changes → Update frontend environment variables

### Message Type Synchronization
- **CRITICAL**: Backend `MessageType` enum changes require frontend message validator updates
- **MANDATORY**: Socket.IO event names must match between frontend and backend
- **REQUIRED**: Data structure changes need validation on both sides
- **FORBIDDEN**: Frontend message types not supported by backend

### State Machine Synchronization
- **CRITICAL**: Backend state changes must be reflected in frontend stores
- **MANDATORY**: State transition events must be handled in frontend
- **REQUIRED**: Error states must have corresponding frontend error handling
- **FORBIDDEN**: Frontend state transitions without backend validation

## AI Decision Guidelines (Full-Stack)

### When Modifying Communication Protocol
1. **FIRST**: Update backend message types and validation
2. **THEN**: Update frontend message validator and websocket service
3. **FINALLY**: Test end-to-end message flow

### When Adding New Features
1. **FIRST**: Determine if feature requires state machine changes
2. **THEN**: Implement backend logic and API endpoints
3. **NEXT**: Create frontend components and store integration
4. **FINALLY**: Test cross-system integration

### When Debugging Cross-System Issues
1. **FIRST**: Check Socket.IO connection status
2. **THEN**: Validate message format compliance
3. **NEXT**: Verify state synchronization
4. **FINALLY**: Check error handling on both sides

## Strict Prohibitions (Full-Stack)

### Architecture Violations
- **NEVER** bypass Socket.IO for frontend-backend communication
- **NEVER** modify state without proper validation on both sides
- **NEVER** exceed 350 lines per file (frontend or backend)
- **NEVER** mix Vue 2 and Vue 3 patterns

### Frontend-Specific Violations
- **NEVER** use jQuery or direct DOM manipulation
- **NEVER** bypass Pinia for global state management
- **NEVER** use inline styles instead of TailwindCSS
- **NEVER** ignore Element Plus design system

### Cross-System Violations
- **NEVER** hardcode backend URLs in frontend
- **NEVER** send unvalidated data between systems
- **NEVER** ignore connection failure scenarios
- **NEVER** bypass message validation layers

## Emergency Procedures (Full-Stack)

### System Reset Protocol
- **FRONTEND**: Clear all stores, reset to initial state, reconnect Socket.IO
- **BACKEND**: Use `StateTransitionEvent.SYSTEM_RESET`
- **COORDINATION**: Frontend must wait for backend state confirmation

### Connection Recovery
- **AUTOMATIC**: Frontend implements exponential backoff reconnection
- **MANUAL**: Provide user option to force reconnection
- **FALLBACK**: Display offline mode with cached data

### Error Recovery
- **FRONTEND**: Show user-friendly error messages, maintain UI responsiveness
- **BACKEND**: Log errors, attempt graceful degradation
- **COORDINATION**: Synchronize error states between systems

---

**CRITICAL REMINDER**: This system handles real-time rehabilitation data. All changes must maintain data integrity and user safety. When in doubt, prioritize system stability over new features.
