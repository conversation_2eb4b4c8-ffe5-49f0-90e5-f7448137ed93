"""
智能康复系统 - 系统状态定义
定义系统状态和状态转换规则
"""
from enum import Enum
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import time

class SystemState(Enum):
    """系统状态枚举"""
    IDLE = "IDLE"                          # 初始状态，建立websocket连接后，会转入WAITING状态
    WAITING = "WAITING"                    # 等待用户识别
    USER_LOGIN = "USER_LOGIN"              # 用户登录验证
    ACTION_PREPARATION = "ACTION_PREPARATION"# 动作准备
    ACTION_TRAINING = "ACTION_TRAINING"    # 动作训练中
    ACTION_REST = "ACTION_REST"            # 动作间休息
    ACTION_COMPLETED = "ACTION_COMPLETED"  # 动作全部结束

class StateTransitionEvent(Enum):
    """状态转换事件枚举"""
    FRONTEND_CONNECTED = "FRONTEND_CONNECTED"  # 前端连接
    USER_DETECTED = "USER_DETECTED"        # 检测到用户
    LOGIN_SUCCESS = "LOGIN_SUCCESS"        # 用户验证成功
    PREPARATION_COMPLETED = "PREPARATION_COMPLETED"  # 准备阶段完成
    TRAINING_STARTED = "TRAINING_STARTED"  # 开始训练
    ACTION_COMPLETED = "ACTION_COMPLETED"  # 动作完成
    SET_COMPLETED = "SET_COMPLETED"        # 组完成
    REST_COMPLETED = "REST_COMPLETED"      # 休息完成
    TASK_SWITCH = "TASK_SWITCH"            # 任务切换
    ALL_TASKS_COMPLETED = "ALL_TASKS_COMPLETED"  # 所有任务完成
    REPORT_GENERATED = "REPORT_GENERATED"  # 报告生成完成
    SYSTEM_RESET = "SYSTEM_RESET"          # 系统重置
    ERROR_OCCURRED = "ERROR_OCCURRED"      # 发生错误
    USER_LEFT = "USER_LEFT"                # 用户离开
# WebSocket消息类型枚举 - 完整版本
class MessageType(str, Enum):
    """WebSocket消息类型枚举"""
    # 核心系统消息
    SYSTEM_STATE = "system_state"
    SYSTEM_INIT = "system_init"

    # 用户相关消息
    USER_DETECTED = "user_detected"
    LOGIN_SUCCESS = "login_success"

    # 准备期间消息
    PREPARATION_PROGRESS = "preparation_progress"
    ACTION_READY = "action_ready"

    # 训练流程消息
    TRAINING_INFO = "training_info"

    ACTION_CHANGED = "action_changed"

    TRAINING_COMPLETED = "training_completed"

    TRAINING_SESSION_ENDED = "training_session_ended"
    # 姿态数据。
    POSE_DATA = "pose_data"

@dataclass
class StateTransition:
    """状态转换定义"""
    from_state: SystemState
    to_state: SystemState
    event: StateTransitionEvent
    condition: Optional[str] = None  # 转换条件描述
    timeout: Optional[float] = None  # 超时时间（秒）

@dataclass
class StateContext:
    """状态上下文信息"""
    current_state: SystemState
    previous_state: Optional[SystemState] = None
    state_start_time: float = 0.0
    state_data: Optional[Dict[str, Any]] = None
    user_info: Optional[Dict[str, Any]] = None
    current_action: Optional[Dict[str, Any]] = None
    action_list: Optional[List[Dict[str, Any]]] = None
    session_id: Optional[str] = None

    def __post_init__(self):
        if self.state_data is None:
            self.state_data = {}
        if self.state_start_time == 0.0:
            self.state_start_time = time.time()

class StateTransitionRules:
    """状态转换规则定义"""
    
    # 定义所有有效的状态转换
    VALID_TRANSITIONS: List[StateTransition] = [
        # 从IDLE状态的转换
        StateTransition(
            from_state=SystemState.IDLE,
            to_state=SystemState.WAITING,
            event=StateTransitionEvent.FRONTEND_CONNECTED,
            condition="前端WebSocket连接建立"
        ),

        # 从WAITING状态的转换
        StateTransition(
            from_state=SystemState.WAITING,
            to_state=SystemState.USER_LOGIN,
            event=StateTransitionEvent.USER_DETECTED,
            condition="检测到有效的patient_id"
        ),
        
        # 从USER_LOGIN状态的转换
        StateTransition(
            from_state=SystemState.USER_LOGIN,
            to_state=SystemState.ACTION_PREPARATION,
            event=StateTransitionEvent.LOGIN_SUCCESS,
            condition="用户验证成功且任务加载完成"
        ),
        StateTransition(
            from_state=SystemState.USER_LOGIN,
            to_state=SystemState.WAITING,
            event=StateTransitionEvent.ERROR_OCCURRED,
            condition="用户验证失败"
        ),
        
        # 从ACTION_PREPARATION状态的转换
        StateTransition(
            from_state=SystemState.ACTION_PREPARATION,
            to_state=SystemState.ACTION_TRAINING,
            event=StateTransitionEvent.PREPARATION_COMPLETED,
            condition="准备倒计时完成，开始训练"
        ),
        StateTransition(
            from_state=SystemState.ACTION_PREPARATION,
            to_state=SystemState.ACTION_TRAINING,
            event=StateTransitionEvent.TRAINING_STARTED,
            condition="手动开始训练或准备时间结束",
            timeout=3.0  # 3秒后自动开始训练
        ),
        StateTransition(
            from_state=SystemState.ACTION_PREPARATION,
            to_state=SystemState.WAITING,
            event=StateTransitionEvent.USER_LEFT,
            condition="用户离开画面"
        ),
        
        # 从ACTION_TRAINING状态的转换
        StateTransition(
            from_state=SystemState.ACTION_TRAINING,
            to_state=SystemState.ACTION_REST,
            event=StateTransitionEvent.SET_COMPLETED,
            condition="完成一组动作，需要休息"
        ),
        StateTransition(
            from_state=SystemState.ACTION_TRAINING,
            to_state=SystemState.ACTION_PREPARATION,
            event=StateTransitionEvent.TASK_SWITCH,
            condition="切换到下一个训练任务"
        ),
        StateTransition(
            from_state=SystemState.ACTION_TRAINING,
            to_state=SystemState.ACTION_COMPLETED,
            event=StateTransitionEvent.ALL_TASKS_COMPLETED,
            condition="所有训练任务完成"
        ),
        StateTransition(
            from_state=SystemState.ACTION_TRAINING,
            to_state=SystemState.WAITING,
            event=StateTransitionEvent.USER_LEFT,
            condition="用户离开画面，暂停训练"
        ),
        
        # 从ACTION_REST状态的转换
        StateTransition(
            from_state=SystemState.ACTION_REST,
            to_state=SystemState.ACTION_TRAINING,
            event=StateTransitionEvent.REST_COMPLETED,
            condition="休息时间结束，继续训练"
        ),
        StateTransition(
            from_state=SystemState.ACTION_REST,
            to_state=SystemState.WAITING,
            event=StateTransitionEvent.USER_LEFT,
            condition="用户离开画面"
        ),
        
        # 从REPORT_GENERATION状态的转换
        StateTransition(
            from_state=SystemState.ACTION_COMPLETED,
            to_state=SystemState.WAITING,
            event=StateTransitionEvent.REPORT_GENERATED,
            condition="报告生成完成，等待下一个用户",
            timeout=10.0  # 10秒后自动返回等待状态
        ),
        
        # 全局错误处理转换
        StateTransition(
            from_state=SystemState.USER_LOGIN,
            to_state=SystemState.WAITING,
            event=StateTransitionEvent.SYSTEM_RESET,
            condition="系统重置"
        ),
        StateTransition(
            from_state=SystemState.ACTION_PREPARATION,
            to_state=SystemState.WAITING,
            event=StateTransitionEvent.SYSTEM_RESET,
            condition="系统重置"
        ),
        StateTransition(
            from_state=SystemState.ACTION_TRAINING,
            to_state=SystemState.WAITING,
            event=StateTransitionEvent.SYSTEM_RESET,
            condition="系统重置"
        ),
        StateTransition(
            from_state=SystemState.ACTION_REST,
            to_state=SystemState.WAITING,
            event=StateTransitionEvent.SYSTEM_RESET,
            condition="系统重置"
        ),
        StateTransition(
            from_state=SystemState.ACTION_COMPLETED,
            to_state=SystemState.WAITING,
            event=StateTransitionEvent.SYSTEM_RESET,
            condition="系统重置"
        )
    ]
    
    @classmethod
    def get_valid_transitions_from_state(cls, state: SystemState) -> List[StateTransition]:
        """获取指定状态的所有有效转换"""
        return [t for t in cls.VALID_TRANSITIONS if t.from_state == state]
    
    @classmethod
    def is_valid_transition(cls, from_state: SystemState, to_state: SystemState, 
                          event: StateTransitionEvent) -> bool:
        """检查状态转换是否有效"""
        for transition in cls.VALID_TRANSITIONS:
            if (transition.from_state == from_state and 
                transition.to_state == to_state and 
                transition.event == event):
                return True
        return False
    
    @classmethod
    def get_transition(cls, from_state: SystemState, event: StateTransitionEvent) -> Optional[StateTransition]:
        """根据当前状态和事件获取转换规则"""
        for transition in cls.VALID_TRANSITIONS:
            if transition.from_state == from_state and transition.event == event:
                return transition
        return None

class StateValidator:
    """状态验证器"""
    
    @staticmethod
    def validate_state_context(context: StateContext) -> bool:
        """验证状态上下文的有效性"""
        if not isinstance(context.current_state, SystemState):
            return False
        
        if context.previous_state and not isinstance(context.previous_state, SystemState):
            return False
        
        if context.state_start_time <= 0:
            return False
        
        return True
    
    @staticmethod
    def validate_transition_event(event: StateTransitionEvent, context: StateContext) -> bool:
        """验证转换事件在当前上下文中是否有效"""
        # 检查是否有对应的转换规则
        transition = StateTransitionRules.get_transition(context.current_state, event)
        if not transition:
            return False
        
        # 可以在这里添加更多的业务逻辑验证
        return True

# 状态描述映射
STATE_DESCRIPTIONS = {
    SystemState.IDLE: "系统初始化",
    SystemState.WAITING: "等待用户识别",
    SystemState.USER_LOGIN: "用户登录验证中",
    SystemState.ACTION_PREPARATION: "任务加载完成，准备训练动作",
    SystemState.ACTION_TRAINING: "动作训练进行中",
    SystemState.ACTION_REST: "组间休息",
    SystemState.ACTION_COMPLETED: "动作全部完成"
}

# 事件描述映射
EVENT_DESCRIPTIONS = {
    StateTransitionEvent.USER_DETECTED: "检测到用户",
    StateTransitionEvent.LOGIN_SUCCESS: "用户登录成功",
    StateTransitionEvent.TRAINING_STARTED: "开始训练",
    StateTransitionEvent.ACTION_COMPLETED: "动作完成",
    StateTransitionEvent.SET_COMPLETED: "组完成",
    StateTransitionEvent.REST_COMPLETED: "休息完成",
    StateTransitionEvent.ALL_TASKS_COMPLETED: "所有任务完成",
    StateTransitionEvent.REPORT_GENERATED: "报告生成完成",
    StateTransitionEvent.SYSTEM_RESET: "系统重置",
    StateTransitionEvent.ERROR_OCCURRED: "发生错误",
    StateTransitionEvent.USER_LEFT: "用户离开"
}
