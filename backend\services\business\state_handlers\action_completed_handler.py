"""
智能康复系统 - ACTION_COMPLETED状态处理器
处理动作完成状态的逻辑
"""
import time
from typing import Dict, Any
from models.system_states import SystemState, StateTransitionEvent
from models.data_models import TrainingReportData
from . import BaseStateHandler

class ActionCompletedHandler(BaseStateHandler):
    """ACTION_COMPLETED状态处理器"""
    
    def __init__(self):
        """初始化ACTION_COMPLETED状态处理器"""
        super().__init__(SystemState.ACTION_COMPLETED)
        self.report_display_duration = 10.0  # 报告显示时间（秒）
    
    def enter_state(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """进入ACTION_COMPLETED状态"""
        self.logger.info("系统进入动作完成状态")
        
        # 生成训练报告
        report_result = self._generate_training_report(context)
        if not report_result["success"]:
            return report_result
        
        # 设置报告显示时间
        report_start_time = time.time()
        context.update({
            "report_start_time": report_start_time,
            "report_end_time": report_start_time + self.report_display_duration,
            "report_active": True,
            "training_report": report_result["report"]
        })
        
        self.logger.info("训练报告生成完成")
        
        return {
            "success": True,
            "message": "所有动作完成，训练报告已生成",
            "training_completed": True,
            "report": report_result["report"],
            "report_display_duration": self.report_display_duration
        }
    
    def _generate_training_report(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """生成训练报告"""
        try:
            user_info = context.get("user_info")
            if not user_info:
                return {
                    "success": False,
                    "message": "用户信息缺失，无法生成报告"
                }
            
            # 获取任务进度和统计信息
            from services.business.task.task_loader import task_loader
            task_progress = task_loader.get_action_progress()
            all_actions = task_loader.get_all_actions()
            
            # 计算总体统计
            total_actions = len(all_actions)
            completed_actions = task_progress.get("completed_actions", 0)
            completion_rate = (completed_actions / total_actions * 100) if total_actions > 0 else 0
            
            # 生成报告数据
            report_id = f"report_{user_info.patient_id}_{int(time.time())}"
            generation_time = time.strftime("%Y-%m-%d %H:%M:%S")
            
            # 简化的报告数据
            report_data = {
                "report_id": report_id,
                "user_id": user_info.patient_id,
                "user_name": user_info.name,
                "generation_time": generation_time,
                "session_info": {
                    "total_actions": total_actions,
                    "completed_actions": completed_actions,
                    "completion_rate": completion_rate,
                    "session_duration": context.get("session_duration", 0)
                },
                "performance": {
                    "overall_score": context.get("total_average_score", 0),
                    "completion_status": "completed" if completion_rate >= 100 else "partial"
                },
                "summary": {
                    "status": "训练完成",
                    "recommendation": "继续保持训练频率" if completion_rate >= 80 else "建议增加训练时间"
                }
            }
            
            self.logger.info(f"训练报告生成成功: {report_id}")
            
            return {
                "success": True,
                "report": report_data
            }
            
        except Exception as e:
            self.logger.error(f"生成训练报告失败: {e}")
            return {
                "success": False,
                "message": f"生成训练报告失败: {str(e)}"
            }
    
    def handle_data(self, data: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理ACTION_COMPLETED状态下的数据"""
        try:
            # 检查报告显示时间是否结束
            current_time = time.time()
            report_end_time = context.get("report_end_time", 0)
            
            if current_time >= report_end_time:
                self.logger.info("报告显示时间结束，准备返回等待状态")
                
                # 清理会话数据
                self._cleanup_session_data(context)
                
                return {
                    "success": True,
                    "message": "训练会话结束，返回等待状态",
                    "report_completed": True,
                    "trigger_event": StateTransitionEvent.REPORT_GENERATED,
                    "next_state": SystemState.WAITING
                }
            
            # 计算剩余显示时间
            remaining_time = report_end_time - current_time
            
            return {
                "success": True,
                "message": f"报告显示中，剩余时间: {remaining_time:.1f}秒",
                "report_active": True,
                "remaining_time": remaining_time
            }
            
        except Exception as e:
            self.logger.error(f"处理完成状态数据失败: {e}")
            return {
                "success": False,
                "message": f"完成状态处理失败: {str(e)}"
            }
    
    def _cleanup_session_data(self, context: Dict[str, Any]):
        """清理会话数据"""
        try:
            # 重置任务加载器
            from services.business.task.task_loader import task_loader
            task_loader.reset_actions()
            
            # 清理上下文中的会话数据
            session_keys = [
                "user_info", "current_action", "task_progress",
                "training_start_time", "session_duration",
                "total_average_score", "training_report"
            ]
            
            for key in session_keys:
                context.pop(key, None)
            
            self.logger.info("会话数据清理完成")
            
        except Exception as e:
            self.logger.error(f"清理会话数据失败: {e}")
    
    def exit_state(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """退出ACTION_COMPLETED状态"""
        self.logger.info("系统退出动作完成状态")
        
        report_data = context.get("training_report")
        user_info = context.get("user_info")
        
        return {
            "success": True,
            "message": "训练会话完成",
            "session_completed": True,
            "user_name": user_info.name if user_info else None,
            "report_generated": report_data is not None
        }
    
    def force_complete_report(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """强制完成报告显示"""
        self.logger.info("强制完成报告显示")
        
        # 清理会话数据
        self._cleanup_session_data(context)
        
        context.update({
            "report_completed": True,
            "report_forced": True
        })
        
        return {
            "success": True,
            "message": "报告显示强制完成",
            "report_completed": True,
            "trigger_event": StateTransitionEvent.REPORT_GENERATED,
            "next_state": SystemState.WAITING
        }
