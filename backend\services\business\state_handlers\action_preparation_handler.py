"""
智能康复系统 - ACTION_PREPARATION状态处理器
处理动作准备状态的逻辑
"""
from typing import Dict, Any
from models.system_states import SystemState, StateTransitionEvent,MessageType
from models.data_models import SystemStateData
from models.data_models import ZMQDetectData
from algorithms.posture_detector import PostureDetector
from . import BaseStateHandler

class ActionPreparationHandler(BaseStateHandler):
    """ACTION_PREPARATION状态处理器"""
    
    def __init__(self):
        """初始化ACTION_PREPARATION状态处理器"""
        super().__init__(SystemState.ACTION_PREPARATION)
        self.preparation_duration = 3.0  # 准备时间（秒）
        self.posture_detector = PostureDetector()
        self.posture_check_enabled = True  # 姿态检查开关
        self.current_action = None
    
    def enter_state(self, context: Dict[str, Any]):
        """进入ACTION_PREPARATION状态"""
        self.logger.info("系统进入动作准备状态")
        # 获取当前任务信息
        self.current_action = context.get("current_action")
    
    def handle_data(self, data: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理ACTION_PREPARATION状态下的数据"""
        try:
            # 只处理姿态数据
            if isinstance(data, ZMQDetectData) and self.posture_check_enabled:
                return self._handle_posture_data(data, context)
            # 非姿态数据，返回等待状态
            return {
                "success": True,
                "message": "等待姿态数据...",
                "preparation_active": True,
                "waiting_for_posture": True
            }
        except Exception as e:
            self.logger.error(f"处理准备状态数据失败: {e}")
            return {
                "success": False,
                "message": f"准备状态处理失败: {str(e)}"
            }
    
    def _handle_posture_data(self, pose_data: ZMQDetectData, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理姿态数据"""
        try:
            # 获取当前动作类型
            current_action = context.get("current_action")
            action_type = current_action.action_info.action_type if current_action else "shoulder_touch"

            keypoints = pose_data.pose_keypoints
            # 执行坐姿检测
            is_sitting = self.posture_detector.detect_preparation_posture(action_type, keypoints)
            # 获取姿态反馈信息
            posture_feedback = self.posture_detector.get_posture_feedback()
            # 检查坐姿是否稳定（维持3秒）
            if is_sitting and self.posture_detector.is_posture_stable():
                self.logger.info("坐姿稳定3秒，准备开始训练")
                return self._trigger_training_start(context,keypoints)

            # 继续准备阶段，提供实时反馈
            return self._continue_preparation_with_feedback(is_sitting, posture_feedback, keypoints)

        except Exception as e:
            self.logger.error(f"姿态数据处理失败: {e}")
            return {
                "success": False,
                "message": f"姿态数据处理失败: {str(e)}"
            }


    def _trigger_training_start(self, context: Dict[str, Any],keypoints) -> Dict[str, Any]:
        """触发训练开始"""
        self.logger.info("动作准备完成，开始训练")
        context.update({
            "preparation_completed": True,
            "training_ready": True
        })

        return {
            "success": True,
            "message": "动作准备完成，开始训练",
            "trigger_event": StateTransitionEvent.PREPARATION_COMPLETED,
            "next_state": SystemState.ACTION_TRAINING,
            "websocket_message": MessageType.ACTION_READY,
            "state_data": SystemStateData(
                current_state=SystemState.ACTION_TRAINING,
                message="动作准备完成，开始训练",
                pose_keypoints=keypoints
            )
        }

    def _continue_preparation_with_feedback(self, is_sitting: bool, posture_feedback: Dict[str, Any],keypoints) -> Dict[str, Any]:
        """继续准备阶段并提供实时反馈"""
        # 初始化默认值，确保变量在所有分支中都有定义
        progress = posture_feedback.get('progress', 0.0)
        sitting_duration = posture_feedback.get('sitting_duration', 0.0)
        required_duration = posture_feedback.get('required_duration', 3.0)

        if is_sitting:
            message = f"检测到坐姿，保持时间: {sitting_duration:.1f}秒/{required_duration:.0f}秒 ({progress*100:.0f}%)"
        else:
            message = "请保持正确的坐姿"
            # 当没有检测到坐姿时，重置进度为0
            progress = 0.0
            sitting_duration = 0.0

        state_data = SystemStateData(
                    current_state=SystemState.ACTION_PREPARATION,
                    message=message,
                    pose_keypoints=keypoints
        )
        state_data.progress_info = {
            "posture_detected": is_sitting,
            "preparation_progress": progress,
            "sitting_duration": sitting_duration,
            "required_duration": required_duration,
            "posture_feedback": posture_feedback
        }
        return {
            "success": True,
            "websocket_message": MessageType.PREPARATION_PROGRESS,
            "state_data": state_data
        }

    def exit_state(self, context: Dict[str, Any]) -> None:
        """退出ACTION_PREPARATION状态"""
        self.logger.info("系统退出动作准备状态")
        # 重置姿态检测状态
        if hasattr(self, 'posture_detector'):
            self.posture_detector.reset_detection_state()
      