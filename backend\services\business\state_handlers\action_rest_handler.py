"""
智能康复系统 - ACTION_REST状态处理器
处理动作间休息状态的逻辑
"""
import time
from typing import Dict, Any
from models.system_states import SystemState, StateTransitionEvent
from . import BaseStateHandler

class ActionRestHandler(BaseStateHandler):
    """ACTION_REST状态处理器"""
    
    def __init__(self):
        """初始化ACTION_REST状态处理器"""
        super().__init__(SystemState.ACTION_REST)
    
    def enter_state(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """进入ACTION_REST状态"""
        self.logger.info("系统进入动作间休息状态")
        
        current_action = context.get("current_action")
        if not current_action:
            self.logger.error("当前任务信息缺失")
            return {
                "success": False,
                "message": "当前任务信息缺失",
                "trigger_event": StateTransitionEvent.ERROR_OCCURRED
            }
        
        # 获取休息时间
        rest_duration = current_action.rest_time
        rest_start_time = time.time()
        rest_end_time = rest_start_time + rest_duration
        
        # 设置休息数据
        context.update({
            "rest_start_time": rest_start_time,
            "rest_duration": rest_duration,
            "rest_end_time": rest_end_time,
            "rest_active": True
        })
        
        self.logger.info(f"开始休息: {rest_duration}秒")
        
        return {
            "success": True,
            "message": f"开始休息，时长: {rest_duration}秒",
            "rest_info": {
                "duration": rest_duration,
                "start_time": rest_start_time,
                "end_time": rest_end_time,
                "current_set": current_action.current_sets,
                "total_sets": current_action.total_sets,
                "action_type": current_action.action_info.action_type
            },
            "rest_started": True,
            "countdown_start": True
        }
    
    def handle_data(self, data: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理ACTION_REST状态下的数据"""
        try:
            # 检查休息时间是否结束
            current_time = time.time()
            rest_end_time = context.get("rest_end_time", 0)
            
            if current_time >= rest_end_time:
                self.logger.info("休息时间结束，准备继续训练")
                
                # 更新上下文
                context.update({
                    "rest_completed": True,
                    "ready_for_next_set": True
                })
                
                return {
                    "success": True,
                    "message": "休息完成，继续训练",
                    "rest_completed": True,
                    "trigger_event": StateTransitionEvent.REST_COMPLETED,
                    "next_state": SystemState.ACTION_TRAINING
                }
            
            # 计算剩余休息时间
            remaining_time = rest_end_time - current_time
            
            return {
                "success": True,
                "message": f"休息中，剩余时间: {remaining_time:.1f}秒",
                "rest_active": True,
                "remaining_time": remaining_time,
                "countdown_update": True
            }
            
        except Exception as e:
            self.logger.error(f"处理休息状态数据失败: {e}")
            return {
                "success": False,
                "message": f"休息状态处理失败: {str(e)}"
            }
    
    def exit_state(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """退出ACTION_REST状态"""
        self.logger.info("系统退出动作间休息状态")
        
        rest_completed = context.get("rest_completed", False)
        rest_duration = context.get("rest_duration", 0)
        actual_rest_time = time.time() - context.get("rest_start_time", 0)
        
        if rest_completed:
            self.logger.info(f"休息完成，实际休息时间: {actual_rest_time:.1f}秒")
            return {
                "success": True,
                "message": "休息阶段完成",
                "rest_completed": True,
                "planned_duration": rest_duration,
                "actual_duration": actual_rest_time
            }
        else:
            self.logger.warning("休息未完成就退出状态")
            return {
                "success": False,
                "message": "休息未完成",
                "rest_completed": False,
                "actual_duration": actual_rest_time
            }
    
    def skip_rest(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """跳过休息时间"""
        self.logger.info("跳过休息时间")
        
        context.update({
            "rest_completed": True,
            "rest_skipped": True,
            "ready_for_next_set": True
        })
        
        return {
            "success": True,
            "message": "休息时间已跳过",
            "rest_completed": True,
            "rest_skipped": True,
            "trigger_event": StateTransitionEvent.REST_COMPLETED,
            "next_state": SystemState.ACTION_TRAINING
        }
    
    def extend_rest(self, additional_time: int, context: Dict[str, Any]) -> Dict[str, Any]:
        """延长休息时间"""
        self.logger.info(f"延长休息时间: {additional_time}秒")
        
        # 更新休息结束时间
        current_end_time = context.get("rest_end_time", 0)
        new_end_time = current_end_time + additional_time
        
        context.update({
            "rest_end_time": new_end_time,
            "rest_extended": True,
            "additional_rest_time": additional_time
        })
        
        return {
            "success": True,
            "message": f"休息时间延长 {additional_time}秒",
            "rest_extended": True,
            "additional_time": additional_time,
            "new_end_time": new_end_time
        }
