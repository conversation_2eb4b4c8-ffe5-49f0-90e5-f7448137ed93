"""
智能康复系统 - ACTION_TRAINING状态处理器
处理动作训练状态的逻辑
"""
import time
from typing import Dict, Any
from models.system_states import SystemState, StateTransitionEvent
from models.data_models import ZMQDetectData
from algorithms.action_recognition import action_recognizer
from algorithms.scoring import action_scorer
from . import BaseStateHandler

class ActionTrainingHandler(BaseStateHandler):
    """ACTION_TRAINING状态处理器"""
    
    def __init__(self):
        """初始化ACTION_TRAINING状态处理器"""
        super().__init__(SystemState.ACTION_TRAINING)
        self.recognition_threshold = 0.8  # 动作识别阈值
        self.min_score_threshold = 60    # 最低分数阈值
    
    def enter_state(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """进入ACTION_TRAINING状态"""
        self.logger.info("系统进入动作训练状态")
        
        current_action = context.get("current_action")
        if not current_action:
            self.logger.error("当前任务信息缺失")
            return {
                "success": False,
                "message": "当前任务信息缺失",
                "trigger_event": StateTransitionEvent.ERROR_OCCURRED
            }
        
        # 初始化训练数据
        context.update({
            "training_start_time": time.time(),
            "training_active": True,
            "current_rep_start_time": time.time(),
            "rep_scores": [],
            "action_detected": False
        })
        
        self.logger.info(f"开始训练: {current_action.action_info.action_type} - 第{current_action.current_sets}组")
        
        return {
            "success": True,
            "message": "动作训练开始",
            "training_info": {
                "action_type": current_action.action_info.action_type,
                "side": current_action.action_info.side,
                "current_set": current_action.current_sets,
                "total_sets": current_action.total_sets,
                "current_rep": current_action.current_reps,
                "target_reps": current_action.reps_per_set,
                "target_score": current_action.action_info.target_score
            },
            "training_started": True
        }
    
    def handle_data(self, data: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理ACTION_TRAINING状态下的姿态数据"""
        try:
            if isinstance(data, ZMQDetectData):
                return self._process_pose_data(data, context)
            
            return {
                "success": True,
                "message": "训练状态，数据已接收",
                "data_type": type(data).__name__
            }
            
        except Exception as e:
            self.logger.error(f"处理训练数据失败: {e}")
            return {
                "success": False,
                "message": f"训练数据处理失败: {str(e)}"
            }
    
    def _process_pose_data(self, pose_data: ZMQDetectData, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理姿态数据进行动作识别和评分"""
        try:
            current_action = context.get("current_action")
            if not current_action:
                return {"success": False, "message": "当前任务信息缺失"}
            
            # 进行动作识别
            recognition_result = action_recognizer.recognize_action(
                pose_data.pose_keypoints,
                current_action.action_info.action_type,
                current_action.action_info.side
            )
            
            if not recognition_result.get("recognized", False):
                return {
                    "success": True,
                    "message": "动作未识别",
                    "action_recognized": False,
                    "recognition_details": recognition_result.get("message", "")
                }
            
            # 动作识别成功，进行评分
            score_result = action_scorer.score_action(
                current_action.action_info.action_type,
                current_action.action_info.side,
                pose_data.pose_keypoints,
                recognition_result
            )
            
            score = score_result.get("score", 0)
            
            # 更新动作数据
            current_action.current_reps += 1
            current_action.current_scores.append(score)
            
            self.logger.info(f"动作完成: 第{current_action.current_reps}次, 得分: {score}")
            
            # 检查是否完成当前组
            if current_action.current_reps >= current_action.reps_per_set:
                return self._complete_current_set(current_action, context)
            
            # 更新上下文
            context.update({
                "current_action": current_action,
                "last_score": score,
                "current_rep_start_time": time.time()
            })
            
            return {
                "success": True,
                "message": f"动作完成，得分: {score}",
                "action_recognized": True,
                "score": score,
                "current_rep": current_action.current_reps,
                "target_reps": current_action.reps_per_set,
                "rep_completed": True
            }
            
        except Exception as e:
            self.logger.error(f"处理姿态数据失败: {e}")
            return {
                "success": False,
                "message": f"姿态数据处理失败: {str(e)}"
            }
    
    def _complete_current_set(self, current_action, context: Dict[str, Any]) -> Dict[str, Any]:
        """完成当前组"""
        try:
            # 计算当前组平均分
            set_average_score = sum(current_action.current_scores) / len(current_action.current_scores)
            
            self.logger.info(f"完成第{current_action.current_sets}组，平均分: {set_average_score:.1f}")
            
            # 检查是否完成所有组
            if current_action.current_sets >= current_action.total_sets:
                return self._complete_all_sets(current_action, context)
            
            # 准备下一组
            current_action.current_sets += 1
            current_action.current_reps = 0
            current_action.set_completed = True
            
            # 更新上下文
            context.update({
                "current_action": current_action,
                "set_completed": True,
                "set_average_score": set_average_score,
                "rest_needed": True
            })
            
            return {
                "success": True,
                "message": f"第{current_action.current_sets - 1}组完成，平均分: {set_average_score:.1f}",
                "set_completed": True,
                "set_average_score": set_average_score,
                "trigger_event": StateTransitionEvent.SET_COMPLETED,
                "next_state": SystemState.ACTION_REST
            }
            
        except Exception as e:
            self.logger.error(f"完成当前组失败: {e}")
            return {
                "success": False,
                "message": f"完成当前组失败: {str(e)}"
            }
    
    def _complete_all_sets(self, current_action, context: Dict[str, Any]) -> Dict[str, Any]:
        """完成所有组"""
        try:
            # 计算总体平均分
            total_average_score = sum(current_action.current_scores) / len(current_action.current_scores)
            
            self.logger.info(f"完成所有组，总平均分: {total_average_score:.1f}")
            
            # 检查是否还有更多任务
            from services.business.task.task_loader import task_loader
            has_next = task_loader.has_next_action()
            
            # 更新上下文
            context.update({
                "current_action": current_action,
                "action_completed": True,
                "total_average_score": total_average_score,
                "has_next_action": has_next
            })
            
            if has_next:
                return {
                    "success": True,
                    "message": f"当前动作完成，总平均分: {total_average_score:.1f}",
                    "action_completed": True,
                    "total_average_score": total_average_score,
                    "trigger_event": StateTransitionEvent.TASK_SWITCH,
                    "next_state": SystemState.ACTION_PREPARATION
                }
            else:
                return {
                    "success": True,
                    "message": f"所有动作完成，总平均分: {total_average_score:.1f}",
                    "all_actions_completed": True,
                    "total_average_score": total_average_score,
                    "trigger_event": StateTransitionEvent.ALL_TASKS_COMPLETED,
                    "next_state": SystemState.ACTION_COMPLETED
                }
                
        except Exception as e:
            self.logger.error(f"完成所有组失败: {e}")
            return {
                "success": False,
                "message": f"完成所有组失败: {str(e)}"
            }
    
    def exit_state(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """退出ACTION_TRAINING状态"""
        self.logger.info("系统退出动作训练状态")
        
        current_action = context.get("current_action")
        training_duration = time.time() - context.get("training_start_time", 0)
        
        return {
            "success": True,
            "message": "动作训练阶段完成",
            "training_duration": training_duration,
            "action_info": current_action.action_info.action_type if current_action else None
        }
