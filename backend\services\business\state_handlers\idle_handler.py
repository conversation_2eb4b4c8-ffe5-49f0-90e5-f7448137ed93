"""
智能康复系统 - IDLE状态处理器
处理系统空闲状态的逻辑
"""
import time
from typing import Dict, Any
from models.system_states import SystemState, StateTransitionEvent, MessageType
from models.data_models import SystemStateData
from . import BaseStateHandler

class IdleHandler(BaseStateHandler):
    """IDLE状态处理器"""
    
    def __init__(self):
        """初始化IDLE状态处理器"""
        super().__init__(SystemState.IDLE)
    
    def enter_state(self, context: Dict[str, Any]):
        """进入IDLE状态"""
        self.logger.info("系统进入空闲状态")
        
    
    def handle_data(self, data: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理IDLE状态下的数据"""
        while (time.time() < context.get("enter_state_time", 0) + 3):
            break
        state_data = SystemStateData(
            current_state=SystemState.WAITING,  # 目标状态
            message=f"系统初始化完成",
        )
        # IDLE状态下不处理任何数据
        return {
            "success": True,
            "message": "空闲状态，忽略数据",
            "trigger_event": StateTransitionEvent.FRONTEND_CONNECTED,
            "next_state": SystemState.WAITING,
            "websocket_message": MessageType.SYSTEM_INIT,
            "state_data": state_data  # 封装好的SystemStateData
        }

  
    def exit_state(self, context: Dict[str, Any]):
        """退出IDLE状态"""
        self.logger.info("系统退出空闲状态")
        
    
