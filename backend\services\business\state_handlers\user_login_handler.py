"""
智能康复系统 - USER_LOGIN状态处理器
处理用户登录验证状态的逻辑
"""
from typing import Dict, Any
from models.system_states import SystemState, StateTransitionEvent, MessageType
from services.business.user.user_manager import user_manager
from services.business.task.task_loader import task_loader
from . import BaseStateHandler

class UserLoginHandler(BaseStateHandler):
    """USER_LOGIN状态处理器"""
    
    def __init__(self):
        """初始化USER_LOGIN状态处理器"""
        super().__init__(SystemState.USER_LOGIN)
    
    def enter_state(self, context: Dict[str, Any]):
        """进入USER_LOGIN状态"""
        self.logger.info("系统进入用户登录验证状态")

        
    def _authenticate_and_load_tasks(self, patient_id: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """验证用户并加载任务"""
        try:
            self.logger.info(f"开始验证用户: {patient_id}")
            # 验证用户身份
            user_info = user_manager.authenticate_user(patient_id)
            if not user_info:
                self.logger.warning(f"用户验证失败: {patient_id}")
                return {
                    "success": False,
                    "message": f"用户验证失败: {patient_id}",
                    "trigger_event": StateTransitionEvent.ERROR_OCCURRED,
                    "next_state": SystemState.WAITING
                }
            # 加载用户任务
            success = task_loader.load_user_actions(patient_id)
            if not success:
                self.logger.warning(f"用户任务加载失败: {patient_id}")
                return {
                    "success": False,
                    "message": f"用户任务加载失败: {patient_id}",
                    "trigger_event": StateTransitionEvent.ERROR_OCCURRED,
                    "next_state": SystemState.WAITING
                }
            
            # 获取当前任务
            current_action = task_loader.get_current_action()
            if not current_action:
                self.logger.warning(f"用户无可用任务: {patient_id}")
                return {
                    "success": False,
                    "message": f"用户无可用任务: {patient_id}",
                    "trigger_event": StateTransitionEvent.ERROR_OCCURRED,
                    "next_state": SystemState.WAITING
                }
            # 更新上下文
            context.update({
                "user_info": user_info,
                "current_action": current_action,
                "action_progress": task_loader.get_action_progress(),
                "login_success": True
            })
            
            self.logger.info(f"用户登录成功: {user_info.name} ({patient_id})")

            # 封装SystemStateData
            from models.data_models import SystemStateData
            import time

            state_data = SystemStateData(
                current_state=SystemState.ACTION_PREPARATION,  # 目标状态
                message=f"欢迎用户 {user_info.name}，任务已加载",
                user_info=user_info,
                current_action=current_action,
                action_list=task_loader.get_all_actions(),
            )

            # 添加登录相关信息到progress_info
            state_data.progress_info = {
                "login_timestamp": time.time(),
            }

            return {
                "success": True,
                "trigger_event": StateTransitionEvent.LOGIN_SUCCESS,
                "next_state": SystemState.ACTION_PREPARATION,
                "websocket_message": MessageType.LOGIN_SUCCESS,
                "state_data": state_data  # 封装好的SystemStateData
            }
            
        except Exception as e:
            self.logger.error(f"用户验证和任务加载失败: {e}")
            return {
                "success": False,
                "message": f"系统错误: {str(e)}",
                "trigger_event": StateTransitionEvent.ERROR_OCCURRED,
                "next_state": SystemState.WAITING
            }
    
    def handle_data(self, data: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理USER_LOGIN状态下的数据"""
        # 登录状态下不处理额外数据
         # 获取检测到的用户ID
        patient_id = context.get("detected_patient_id")
        if not patient_id:
            self.logger.error("未找到检测到的用户ID")
            return {
                "success": False,
                "message": "用户ID缺失",
                "trigger_event": StateTransitionEvent.ERROR_OCCURRED,
                "next_state": SystemState.WAITING
            }
        
        # 执行用户验证和任务加载
        return self._authenticate_and_load_tasks(patient_id, context)
    
    def exit_state(self, context: Dict[str, Any]) :
        """退出USER_LOGIN状态"""
        self.logger.info("系统退出用户登录验证状态")
        
        