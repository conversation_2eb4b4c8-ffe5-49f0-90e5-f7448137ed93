"""
智能康复系统 - WAITING状态处理器
处理等待用户识别状态的逻辑
"""
from typing import Dict, Any
from models.system_states import SystemState, StateTransitionEvent
from models.data_models import ZMQDetectData
from . import BaseStateHandler
from models.system_states import MessageType
import time
class WaitingHandler(BaseStateHandler):
    """WAITING状态处理器"""
    
    def __init__(self):
        """初始化WAITING状态处理器"""
        super().__init__(SystemState.WAITING)
        self.detection_threshold = 3  # 连续检测次数阈值
        self.detection_count = 0
        self.last_patient_id = None
    
    def enter_state(self, context: Dict[str, Any]):
        """进入WAITING状态"""
        self.logger.info("系统进入等待用户识别状态")
        # 重置检测计数
        self.detection_count = 0
        self.last_patient_id = None
        
    
    def handle_data(self, data: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理WAITING状态下的姿态数据"""
        try:
      
            # 检查是否为姿态检测数据
            if isinstance(data, ZMQDetectData):
                return self._handle_pose_detection(data, context)
            
            return {
                "success": True,
                "message": "等待状态，数据已接收",
                "data_type": type(data).__name__
            }
            
        except Exception as e:
            self.logger.error(f"处理等待状态数据失败: {e}")
            return {
                "success": False,
                "message": f"数据处理失败: {str(e)}"
            }
    
    def _handle_pose_detection(self, pose_data: ZMQDetectData, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理姿态检测数据"""
        try:
            while(time.time() < context.get("enter_state_time", 0) + 2):
                break
            patient_id = pose_data.patient_id
            # 检查是否检测到有效用户ID
            if not patient_id or patient_id == "unknown":
                self.detection_count = 0
                self.last_patient_id = None
                return {
                    "success": True,
                    "message": "未检测到有效用户",
                    "user_detected": False
                }
            
            # 检查是否为连续的同一用户
            if patient_id == self.last_patient_id:
                self.detection_count += 1
            else:
                self.detection_count = 1
                self.last_patient_id = patient_id
            
            self.logger.debug(f"用户检测: {patient_id}, 连续次数: {self.detection_count}")
            
            # 检查是否达到检测阈值
            if self.detection_count >= self.detection_threshold:
                self.logger.info(f"用户检测成功: {patient_id}")
                # 更新上下文
                context.update({
                    "detected_patient_id": patient_id,
                    "detection_time": pose_data.timestamp,
                })
                # 封装SystemStateData
                from models.data_models import SystemStateData,UserInfo
                user_info = UserInfo(
                    patient_id=patient_id,
                    name="",
                    age=None,
                    gender=None,
                    last_login=None
                )
                state_data = SystemStateData(
                    current_state=SystemState.USER_LOGIN,  # 目标状态
                    message=f"检测到用户: {patient_id}",
                    user_info=user_info,  # 用户信息将在登录成功后设置
                )
                return {
                    "success": True,
                    "trigger_event": StateTransitionEvent.USER_DETECTED,
                    "next_state": SystemState.USER_LOGIN,
                    "websocket_message": MessageType.USER_DETECTED,
                    "state_data": state_data  # 封装好的SystemStateData
                }
            return {
                "success": True,
                "message": f"正在检测用户: {patient_id} ({self.detection_count}/{self.detection_threshold})",
                "user_detected": False,
                "detection_progress": self.detection_count / self.detection_threshold
            }
            
        except Exception as e:
            self.logger.error(f"处理姿态检测失败: {e}")
            return {
                "success": False,
                "message": f"姿态检测处理失败: {str(e)}"
            }
    
    def exit_state(self, context: Dict[str, Any]):
        """退出WAITING状态"""
        self.logger.info("系统退出等待用户识别状态")
        

    
    def reset_detection(self):
        """重置检测状态"""
        self.detection_count = 0
        self.last_patient_id = None
        self.logger.debug("用户检测状态已重置")
