"""
智能康复系统 - 任务加载服务
处理任务加载、验证和管理
"""
import logging
from typing import List, Optional, Dict, Any
from services.business.user.user_manager import user_manager
from models.data_models import ActionInfo, CurrentAction, UserInfo
from models.constants import ActionKeyPoints
import time
class ActionLoader:
    """任务加载器"""
    
    def __init__(self):
        """初始化任务加载器"""
        self.logger = logging.getLogger(__name__)
        self.current_action_index = 0
        self.loaded_actions: List[ActionInfo] = []
        self.current_user: Optional[UserInfo] = None
        
        self.logger.info("任务加载器初始化完成")
    
    def load_user_actions(self, patient_id: str) -> bool:
        """
        加载用户任务
        
        Args:
            patient_id: 患者ID
            
        Returns:
            bool: 加载是否成功
        """
        try:
    
            # 获取用户任务
            actions = user_manager.get_user_actions(patient_id)
            if not actions:
                self.logger.warning(f"用户无分配任务: {patient_id}")
                return False
            
            self.loaded_actions = actions
            self.current_action_index = 0
            
            self.logger.info(f"加载用户任务成功: {patient_id} - {len(actions)} 个任务")
            return True
            
        except Exception as e:
            self.logger.error(f"加载用户任务失败: {e}")
            return False
    
    def get_current_action(self) -> Optional[CurrentAction]:
        """
        获取当前任务
        
        Returns:
            CurrentAction: 当前任务，无任务返回None
        """
        try:
            if not self.loaded_actions or self.current_action_index >= len(self.loaded_actions):
                return None
            current_action_info = self.loaded_actions[self.current_action_index]
            current_action = CurrentAction(
                action_info=current_action_info,
                current_sets=1,  # 初始为第1组（从1开始计数）
                current_reps=0,  # 初始为0次
                total_sets=current_action_info.sets,
                reps_per_set=current_action_info.reps_per_set,
                # 新增休息相关配置
                rest_time=current_action_info.rest_time,  # 休息时长 
                set_completed=False,  # 当前组是否完成
                current_scores=[],  # 每次分数列表
                action_status="active",  # 动作状态: pending, active, completed, failed
                start_time=time.time()  # 动作开始时间
            )
            return current_action
            
        except Exception as e:
            self.logger.error(f"获取当前任务失败: {e}")
            return None
    
    def get_next_action(self) -> Optional[CurrentAction]:
        """
        获取下一个任务

        Returns:
            Currentaction: 下一个任务，无更多任务返回None
        """
        try:
            if not self.loaded_actions:
                return None
            self.current_action_index += 1

            if self.current_action_index >= len(self.loaded_actions):
                self.logger.info("所有任务已完成")
                return None

            return self.get_current_action()

        except Exception as e:
            self.logger.error(f"获取下一个任务失败: {e}")
            return None

    def next_action(self) -> bool:
        """
        切换到下一个动作

        Returns:
            bool: 切换成功返回True，无更多任务返回False
        """
        try:
            next_action = self.get_next_action()
            if next_action:
                self.logger.info(f"切换到下一个任务: {next_action.action_info}")
                return True
            else:
                self.logger.info("没有更多任务")
                return False
        except Exception as e:
            self.logger.error(f"任务切换失败: {e}")
            return False

    def has_next_action(self) -> bool:
        """
        检查是否有下一个动作

        Returns:
            bool: 有下一个任务返回True，否则返回False
        """
        try:
            if not self.loaded_actions:
                return False
            return (self.current_action_index + 1) < len(self.loaded_actions)
        except Exception as e:
            self.logger.error(f"检查下一个任务失败: {e}")
            return False
    
    def get_action_progress(self) -> Dict[str, Any]:
        """
        获取任务进度
        
        Returns:
            Dict: 任务进度信息
        """
        try:
            if not self.loaded_actions:
                return {
                    'total_actions': 0,
                    'completed_actions': 0,
                    'current_action_index': 0,
                    'progress_percentage': 0
                }
            
            total_actions = len(self.loaded_actions)
            completed_actions = self.current_action_index
            progress_percentage = (completed_actions / total_actions) * 100 if total_actions > 0 else 0
            
            return {
                'total_actions': total_actions,
                'completed_actions': completed_actions,
                'current_action_index': self.current_action_index,
                'progress_percentage': progress_percentage
            }
            
        except Exception as e:
            self.logger.error(f"获取任务进度失败: {e}")
            return {}
    
    def get_action_details(self, action_index: Optional[int] = None) -> Optional[Dict[str, Any]]:
        """
        获取任务详细信息
        
        Args:
            action_index: 任务索引，None表示当前任务
            
        Returns:
            Dict: 任务详细信息
        """
        try:
            if not self.loaded_actions:
                return None
            
            index = action_index if action_index is not None else self.current_action_index
            
            if index < 0 or index >= len(self.loaded_actions):
                return None
            
            action_info = self.loaded_actions[index]
            
            # 获取动作关键点
            action_keypoints = self._get_action_keypoints(action_info.action_type, action_info.side)
            
            return {
                'action_info': {
                    'action_type': action_info.action_type,
                    'side': action_info.side,
                    'sets': action_info.sets,
                    'reps_per_set': action_info.reps_per_set,
                    'rest_time': action_info.rest_time,
                    'target_score': action_info.target_score,
                    'required_keypoints': action_info.required_keypoints
                },
                'action_keypoints': action_keypoints,
                'action_index': index
            }
            
        except Exception as e:
            self.logger.error(f"获取任务详细信息失败: {e}")
            return None
    
    def _get_action_keypoints(self, action_type: str, side: str) -> List[int]:
        """
        获取动作关键点
        
        Args:
            action_type: 动作类型
            side: 训练侧别
            
        Returns:
            List[int]: 关键点索引列表
        """
        try:
            # 构建关键点映射键
            keypoint_key = f"{action_type.upper()}_{side.upper()}"
            
            # 从ActionKeyPoints获取关键点
            if hasattr(ActionKeyPoints, keypoint_key):
                return getattr(ActionKeyPoints, keypoint_key)
            else:
                self.logger.warning(f"未找到动作关键点映射: {keypoint_key}")
                return []
                
        except Exception as e:
            self.logger.error(f"获取动作关键点失败: {e}")
            return []
    
    def validate_action_completion(self, current_action: CurrentAction) -> bool:
        """
        验证任务是否完成
        
        Args:
            current_action: 当前任务
            
        Returns:
            bool: 任务是否完成
        """
        try:
            # 检查是否完成所有组和次数
            if (current_action.current_sets >= current_action.total_sets and 
                current_action.current_reps >= current_action.reps_per_set):
                return True
            return False
        except Exception as e:
            self.logger.error(f"验证任务完成失败: {e}")
            return False
    
    def reset_actions(self):
        """重置任务状态"""
        try:
            self.current_action_index = 0
            self.loaded_actions = []
            self.current_user = None
            
            self.logger.info("任务状态已重置")
            
        except Exception as e:
            self.logger.error(f"重置任务状态失败: {e}")

    
    def get_all_actions(self) -> List[ActionInfo]:
        """
        获取所有加载的任务
        
        Returns:
            List[actionInfo]: 任务列表
        """
        return self.loaded_actions.copy()
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取任务加载器统计信息
        
        Returns:
            Dict: 统计信息
        """
        return {
            'loaded_actions_count': len(self.loaded_actions),
            'current_action_index': self.current_action_index,
            'has_user': self.current_user is not None,
            'user_id': self.current_user.patient_id if self.current_user else None,
            'progress': self.get_action_progress()
        }

# 全局任务加载器实例
task_loader = ActionLoader()
