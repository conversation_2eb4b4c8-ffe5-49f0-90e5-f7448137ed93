# Socket.IO 迁移指南

## 🔧 问题解决

### 原问题
```
WebSocket connection to 'ws://localhost:5000/' failed
```

**原因**: 前端使用原生WebSocket连接Socket.IO服务器，协议不兼容。

### 解决方案
将前端WebSocket服务迁移到Socket.IO客户端。

## 📦 安装依赖

### 1. 安装Socket.IO客户端
```bash
cd frontend
npm install socket.io-client@^4.7.4
```

### 2. 验证安装
检查 `package.json` 中是否包含：
```json
{
  "dependencies": {
    "socket.io-client": "^4.7.4"
  }
}
```

## 🔄 代码迁移

### 1. WebSocket服务迁移
**之前 (原生WebSocket)**:
```javascript
// websocket.js
this.ws = new WebSocket(this.url)
this.ws.send(JSON.stringify(message))
```

**之后 (Socket.IO)**:
```javascript
// websocket.js
import { io } from 'socket.io-client'

this.socket = io(this.url, {
  transports: ['websocket', 'polling'],
  reconnection: true
})
this.socket.emit(eventType, data)
```

### 2. 消息发送方式变更
**之前**:
```javascript
websocketService.send({
  type: 'user_login',
  data: { userId: '123' }
})
```

**之后**:
```javascript
websocketService.send('user_login', { userId: '123' })
```

### 3. 事件监听方式
**之前**:
```javascript
// 监听JSON消息
this.ws.onmessage = (event) => {
  const message = JSON.parse(event.data)
  this._emit(message.message_type, message.data)
}
```

**之后**:
```javascript
// 监听特定事件
this.supportedMessageTypes.forEach(messageType => {
  this.socket.on(messageType, (data) => {
    this._handleMessage(messageType, data)
  })
})
```

## 🔗 连接配置

### Socket.IO连接选项
```javascript
const socket = io('http://localhost:5000', {
  transports: ['websocket', 'polling'],  // 传输方式
  timeout: 10000,                        // 连接超时
  reconnection: true,                    // 自动重连
  reconnectionAttempts: 5,               // 重连次数
  reconnectionDelay: 3000                // 重连间隔
})
```

### 支持的事件类型
```javascript
const supportedMessageTypes = [
  'system_state',
  'user_detected', 
  'login_success',
  'action_ready',
  'training_info',
  'action_changed',
  'rest_time',
  'training_completed',
  'training_session_ended'
]
```

## 📡 使用示例

### 1. 在Vue组件中使用
```vue
<script setup>
import { useWebSocket } from '@/utils/useWebSocket'

const { connect, send, subscribe, isConnected } = useWebSocket()

// 连接Socket.IO
await connect()

// 发送消息
send('user_login', { userId: 'user123' })

// 监听消息
subscribe('system_state', (data) => {
  console.log('系统状态更新:', data)
})
</script>
```

### 2. 在Pinia Store中使用
```javascript
// stores/systemStore.js
import websocketService from '@/services/websocket'

export const useSystemStore = defineStore('system', () => {
  const connectWebSocket = async () => {
    await websocketService.connect()
    
    // 监听系统状态
    websocketService.on('system_state', (data) => {
      updateSystemState(data)
    })
  }
  
  const sendUserLogin = (userId) => {
    websocketService.send('user_login', { userId })
  }
  
  return { connectWebSocket, sendUserLogin }
})
```

## 🔍 调试和测试

### 1. 检查连接状态
```javascript
// 在浏览器控制台中
console.log('Socket.IO连接状态:', websocketService.isConnected)
console.log('Socket.IO实例:', websocketService.socket)
```

### 2. 监听连接事件
```javascript
websocketService.on('connected', () => {
  console.log('Socket.IO连接成功')
})

websocketService.on('error', (error) => {
  console.error('Socket.IO连接错误:', error)
})
```

### 3. 测试消息发送
```javascript
// 发送测试消息
websocketService.send('ping', { timestamp: Date.now() })
```

## ⚠️ 注意事项

### 1. URL格式变更
- **WebSocket**: `ws://localhost:5000`
- **Socket.IO**: `http://localhost:5000` (自动处理协议升级)

### 2. 消息格式变更
- **WebSocket**: JSON字符串
- **Socket.IO**: 直接传递JavaScript对象

### 3. 事件处理变更
- **WebSocket**: 单一`onmessage`事件
- **Socket.IO**: 多个命名事件

### 4. 重连机制
- **WebSocket**: 手动实现重连逻辑
- **Socket.IO**: 内置自动重连机制

## 🚀 启动步骤

### 1. 安装依赖
```bash
cd frontend
npm install
```

### 2. 启动前端
```bash
npm run dev
```

### 3. 启动后端
```bash
cd backend
# 激活虚拟环境
venv\Scripts\activate
# 启动Socket.IO服务器
python app.py
```

### 4. 验证连接
1. 打开浏览器访问 `http://localhost:3000`
2. 打开开发者工具查看控制台
3. 应该看到 "Socket.IO连接成功" 消息

## 🔧 故障排除

### 问题1: 连接超时
**解决方案**: 检查后端Socket.IO服务是否正常启动

### 问题2: CORS错误
**解决方案**: 确保后端Socket.IO配置了正确的CORS设置

### 问题3: 消息发送失败
**解决方案**: 检查事件名称是否与后端一致

### 问题4: 重连失败
**解决方案**: 检查网络连接和服务器状态

## ✅ 迁移完成检查清单

- [x] 安装Socket.IO客户端依赖
- [x] 更新WebSocket服务使用Socket.IO
- [x] 修改消息发送方法
- [x] 更新事件监听机制
- [x] 修改useWebSocket组合式API
- [x] 测试连接和消息传输
- [x] 验证重连机制
- [x] 更新文档和注释

迁移完成后，前端将能够正常连接到Socket.IO后端服务器，解决WebSocket连接失败的问题。
