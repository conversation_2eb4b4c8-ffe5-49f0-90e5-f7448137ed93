# 智能康复系统整体架构

## 概述

智能康复系统是一个基于状态机驱动的实时康复训练系统，采用现代化的全栈架构设计，支持姿态检测、动作识别、实时训练反馈与评分等核心功能。

## 技术栈组成

### 后端技术栈
- **Web框架**: Flask - 轻量级Python Web框架
- **实时通信**: SocketIO - WebSocket实时双向通信
- **消息队列**: ZMQ (ZeroMQ) - 高性能异步消息传递
- **计算机视觉**: OpenCV - 图像处理和计算机视觉
- **数值计算**: NumPy - 科学计算基础库

### 前端技术栈
- **框架**: Vue 3 - 渐进式JavaScript框架
- **UI组件库**: Element Plus - Vue 3组件库
- **样式框架**: TailwindCSS - 实用优先的CSS框架
- **状态管理**: Pinia - Vue官方状态管理库
- **构建工具**: Vite - 下一代前端构建工具

### 通信协议
- **前后端通信**: WebSocket - 实时双向通信
- **内部组件通信**: ZMQ - 高性能消息传递
- **HTTP API**: RESTful API - 标准Web API

## 系统整体架构图

```mermaid
graph TB
    subgraph "外部数据源层"
        A1[姿态检测算法模块]
        A2[摄像头数据采集]
        A3[传感器数据]
    end
    
    subgraph "数据接入层"
        B1[ZMQ端口6070<br/>检测数据接收]
        B2[ZMQ端口6080<br/>摄像头数据接收]
    end
    
    subgraph "后端应用层"
        subgraph "系统启动管理"
            C1[SystemLauncher<br/>系统启动器]
            C2[ConfigManager<br/>配置管理器]
            C3[LoggerSetup<br/>日志系统]
        end
        
        subgraph "通信服务层"
            D1[ZMQReceiver<br/>ZMQ数据接收器]
            D2[WebSocketHandler<br/>WebSocket处理器]
            D3[MJPEGStream<br/>视频流服务]
        end
        
        subgraph "业务逻辑层"
            E1[SystemCoordinator<br/>系统协调器]
            E2[StateManager<br/>状态管理器]
            E3[StateHandlers<br/>状态处理器集合]
            E4[TaskLoader<br/>任务加载器]
            E5[UserManager<br/>用户管理器]
        end
        
        subgraph "数据模型层"
            F1[SystemStates<br/>系统状态定义]
            F2[DataModels<br/>数据模型]
            F3[MessageTypes<br/>消息类型]
        end
        
        subgraph "Web应用层"
            G1[Flask App<br/>Web应用]
            G2[SocketIO<br/>WebSocket服务]
            G3[HTTP Routes<br/>API路由]
        end
    end
    
    subgraph "网络通信层"
        H1[WebSocket连接]
        H2[HTTP连接]
    end
    
    subgraph "前端应用层"
        subgraph "网络服务层"
            I1[WebSocket客户端]
            I2[HTTP客户端]
        end
        
        subgraph "状态管理层"
            J1[Main Store<br/>主状态管理]
            J2[User Store<br/>用户状态]
            J3[System Store<br/>系统状态]
        end
        
        subgraph "路由管理层"
            K1[Vue Router<br/>路由管理器]
            K2[Route Guards<br/>路由守卫]
        end
        
        subgraph "组件层"
            L1[页面组件<br/>Views]
            L2[业务组件<br/>Business Components]
            L3[通用组件<br/>Common Components]
            L4[可视化组件<br/>Visualization Components]
        end
        
        subgraph "UI渲染层"
            M1[Element Plus组件]
            M2[TailwindCSS样式]
            M3[用户界面]
        end
    end
    
    %% 数据流连接
    A1 --> B1
    A2 --> B2
    A3 --> B1
    
    B1 --> D1
    B2 --> D1
    
    D1 --> E1
    D2 --> E1
    E1 --> E2
    E1 --> E3
    E1 --> E4
    E1 --> E5
    
    E3 --> F1
    E3 --> F2
    D2 --> F3
    
    E1 --> D2
    D2 --> G2
    G1 --> G2
    G1 --> G3
    
    G2 --> H1
    G3 --> H2
    
    H1 --> I1
    H2 --> I2
    
    I1 --> J1
    I2 --> J1
    J1 --> J2
    J1 --> J3
    
    J1 --> K1
    K1 --> K2
    K2 --> L1
    
    L1 --> L2
    L1 --> L3
    L1 --> L4
    
    L2 --> M1
    L3 --> M1
    L4 --> M1
    M1 --> M2
    M2 --> M3
    
    %% 管理连接
    C1 --> C2
    C1 --> C3
    C1 --> D1
    C1 --> D2
    C1 --> E1
    C1 --> G1
    
    %% 样式定义
    classDef external fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef backend fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef frontend fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef communication fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef data fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    
    class A1,A2,A3 external
    class C1,C2,C3,D1,D2,D3,E1,E2,E3,E4,E5,G1,G2,G3 backend
    class I1,I2,J1,J2,J3,K1,K2,L1,L2,L3,L4,M1,M2,M3 frontend
    class B1,B2,H1,H2 communication
    class F1,F2,F3 data
```

## 核心设计原则

### 1. 状态机驱动架构
- **状态集中管理**: 所有业务逻辑基于明确定义的系统状态
- **事件驱动转换**: 通过事件触发状态转换，确保流程可控
- **状态处理器模式**: 每个状态对应独立的处理器，职责清晰

### 2. 分层架构设计
- **数据接入层**: 负责外部数据的接收和初步处理
- **业务逻辑层**: 核心业务逻辑处理和状态管理
- **通信服务层**: 处理各种通信协议和数据传输
- **表现层**: 用户界面和交互逻辑

### 3. 实时通信保障
- **WebSocket双向通信**: 前后端实时数据同步
- **ZMQ高性能消息**: 内部组件间高效数据传递
- **异步处理机制**: 避免阻塞，提升系统响应性

### 4. 模块化组件设计
- **松耦合架构**: 组件间依赖最小化
- **高内聚设计**: 单一职责原则
- **标准化接口**: 统一的数据格式和通信协议

## 关键特性

### 🎯 实时性能
- **低延迟通信**: WebSocket + ZMQ确保毫秒级响应
- **异步处理**: 非阻塞式数据处理流程
- **并发支持**: 多用户同时使用支持

### 🔄 可扩展性
- **模块化设计**: 易于添加新功能和状态
- **插件化架构**: 支持算法模块热插拔
- **水平扩展**: 支持多实例部署

### 🛡️ 可靠性
- **错误恢复**: 完善的异常处理和恢复机制
- **状态一致性**: 确保前后端状态同步
- **监控告警**: 全面的系统监控和日志记录

### 🎨 用户体验
- **响应式设计**: 适配不同设备和屏幕
- **实时反馈**: 即时的训练反馈和指导
- **直观界面**: 基于Element Plus的现代化UI

## 数据流概览

```mermaid
flowchart LR
    A[外部数据源] --> B[ZMQ接收]
    B --> C[系统协调器]
    C --> D[状态处理器]
    D --> E[WebSocket发送]
    E --> F[前端Store]
    F --> G[UI组件]
    G --> H[用户界面]
    
    style A fill:#e1f5fe
    style B fill:#fff3e0
    style C fill:#f3e5f5
    style D fill:#f3e5f5
    style E fill:#fff3e0
    style F fill:#e8f5e8
    style G fill:#e8f5e8
    style H fill:#e8f5e8
```

## 架构优势

1. **清晰的职责分离**: 每个组件职责明确，便于开发和维护
2. **强大的实时能力**: 支持实时数据处理和用户交互
3. **良好的可扩展性**: 模块化设计支持功能扩展
4. **现代化技术栈**: 采用最新的前后端技术
5. **完善的状态管理**: 状态机确保业务逻辑清晰可控

## 相关文档

- [详细数据流图](./data_flow_diagram.md)
- [组件交互时序图](./component_interaction.md)
- [状态机转换图](./state_machine_diagram.md)
- [组件依赖关系图](./component_dependencies.md)
- [部署架构图](./deployment_architecture.md)
- [架构优化建议](./architecture_optimization.md)
