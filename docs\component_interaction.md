# 智能康复系统组件交互时序图

## 概述

本文档详细描述了智能康复系统中各组件间的交互时序，包括系统启动流程、实时数据处理流程、前端连接处理流程和状态转换流程等关键场景的时序图。

## 1. 系统启动时序图

### 1.1 完整启动流程

```mermaid
sequenceDiagram
    participant Main as run.py
    participant Launcher as SystemLauncher
    participant Config as ConfigManager
    participant Logger as LoggerSetup
    participant Models as DataModels
    participant Comm as CommunicationServices
    participant Business as BusinessServices
    participant Flask as FlaskApp
    participant ZMQ as ZMQReceiver
    participant WS as WebSocketHandler
    participant Coord as SystemCoordinator
    
    Note over Main,Coord: 系统启动阶段
    Main->>Launcher: 创建启动器实例
    Launcher->>Config: 加载配置文件
    Config-->>Launcher: 返回配置数据
    
    Launcher->>Logger: 初始化日志系统
    Logger-->>Launcher: 日志配置完成
    
    Note over Launcher,Models: 数据模型初始化
    Launcher->>Models: 初始化数据模型
    Models->>Models: 验证数据结构
    Models-->>Launcher: 模型验证完成
    
    Note over Launcher,Comm: 通信服务初始化（优先级高）
    Launcher->>Comm: 初始化通信服务
    Comm->>WS: 创建WebSocket处理器
    WS-->>Comm: WebSocket处理器就绪
    Comm->>ZMQ: 创建ZMQ接收器
    ZMQ-->>Comm: ZMQ接收器就绪
    Comm-->>Launcher: 通信服务初始化完成
    
    Note over Launcher,Business: 业务服务初始化
    Launcher->>Business: 初始化业务服务
    Business->>Coord: 创建系统协调器
    Coord-->>Business: 系统协调器就绪
    Business->>Coord: 建立WebSocket连接
    Coord->>WS: initialize_websocket()
    WS-->>Coord: 连接建立成功
    Business-->>Launcher: 业务服务初始化完成
    
    Note over Launcher,Flask: Web应用启动
    Launcher->>Flask: 初始化Flask应用
    Flask->>Flask: 配置路由和中间件
    Flask-->>Launcher: Web应用就绪
    
    Note over Launcher,ZMQ: 后台服务启动
    Launcher->>ZMQ: 启动后台服务
    ZMQ->>ZMQ: 设置数据回调
    ZMQ->>Coord: 注册回调函数
    Coord-->>ZMQ: 回调注册成功
    ZMQ-->>Launcher: 后台服务启动完成
    
    Launcher->>Flask: 启动Web服务器
    Flask->>Flask: 监听指定端口
    Flask-->>Main: 系统启动完成
```

### 1.2 组件初始化依赖关系

```mermaid
sequenceDiagram
    participant Launcher as SystemLauncher
    participant WS as WebSocketHandler
    participant Coord as SystemCoordinator
    participant State as StateManager
    participant Handler as StateHandlers
    participant Task as TaskLoader
    participant User as UserManager
    
    Note over Launcher,User: 组件依赖初始化顺序
    
    Launcher->>WS: 1. 优先创建WebSocket处理器
    WS-->>Launcher: WebSocket处理器就绪
    
    Launcher->>State: 2. 创建状态管理器
    State-->>Launcher: 状态管理器就绪
    
    Launcher->>Handler: 3. 创建状态处理器工厂
    Handler-->>Launcher: 状态处理器就绪
    
    Launcher->>Task: 4. 创建任务加载器
    Task-->>Launcher: 任务加载器就绪
    
    Launcher->>User: 5. 创建用户管理器
    User-->>Launcher: 用户管理器就绪
    
    Launcher->>Coord: 6. 创建系统协调器
    Coord->>State: 注入状态管理器
    Coord->>Handler: 注入状态处理器工厂
    Coord->>Task: 注入任务加载器
    Coord->>User: 注入用户管理器
    Coord-->>Launcher: 系统协调器初始化完成
    
    Launcher->>Coord: 7. 建立WebSocket连接
    Coord->>WS: initialize_websocket()
    WS->>Coord: 设置系统协调器引用
    WS-->>Coord: 双向引用建立成功
    Coord-->>Launcher: 组件连接完成
```

## 2. 实时数据处理时序图

### 2.1 姿态数据处理流程

```mermaid
sequenceDiagram
    participant External as 外部算法模块
    participant ZMQ as ZMQ接收器
    participant Coord as 系统协调器
    participant State as 状态管理器
    participant Handler as 状态处理器
    participant WS as WebSocket处理器
    participant Frontend as 前端Store
    
    Note over External,Frontend: 实时姿态数据处理流程
    
    External->>ZMQ: 发送ZMQDetectData
    ZMQ->>ZMQ: 数据验证和解析
    ZMQ->>Coord: handle_pose_data(pose_data)
    
    Coord->>State: get_current_state()
    State-->>Coord: 返回当前状态
    
    Coord->>Handler: 获取当前状态处理器
    Handler-->>Coord: 返回处理器实例
    
    Coord->>Handler: handle_data(pose_data, context)
    Handler->>Handler: 执行业务逻辑处理
    
    alt 处理成功
        Handler-->>Coord: 返回成功结果+状态数据
        Coord->>Coord: _send_data_result()
        Coord->>WS: send_state_message()
        WS->>WS: 消息序列化
        WS->>Frontend: WebSocket消息发送
        Frontend->>Frontend: 状态更新
        
        alt 需要状态转换
            Coord->>Coord: _check_state_transition()
            Coord->>State: transition_to(event)
            State->>State: 执行状态转换
            State->>Handler: 新状态处理器.enter_state()
            Handler-->>State: 状态初始化完成
            State-->>Coord: 状态转换成功
        end
    else 处理失败
        Handler-->>Coord: 返回错误结果
        Coord->>Coord: 记录错误日志
        Coord->>WS: 发送错误消息
        WS->>Frontend: 错误状态通知
    end
```

### 2.2 摄像头数据处理流程

```mermaid
sequenceDiagram
    participant Camera as 摄像头模块
    participant ZMQ as ZMQ接收器
    participant Coord as 系统协调器
    participant MJPEG as MJPEG流服务
    participant Frontend as 前端视频组件
    
    Note over Camera,Frontend: 摄像头数据流处理
    
    Camera->>ZMQ: 发送ZMQCameraFrame
    ZMQ->>ZMQ: 帧数据验证
    ZMQ->>Coord: handle_camera_frame(frame_data)
    
    Coord->>MJPEG: 更新视频流
    MJPEG->>MJPEG: 帧数据编码
    MJPEG->>Frontend: HTTP流推送
    Frontend->>Frontend: 视频显示更新
    
    Note over Coord: 可选：帧数据分析
    Coord->>Coord: 帧质量检测
    alt 帧质量异常
        Coord->>Frontend: 发送质量警告
    end
```

## 3. 前端连接处理时序图

### 3.1 前端连接建立流程

```mermaid
sequenceDiagram
    participant Frontend as 前端应用
    participant WS as WebSocket处理器
    participant Coord as 系统协调器
    participant State as 状态管理器
    participant IdleHandler as Idle处理器
    
    Note over Frontend,IdleHandler: 前端连接建立流程
    
    Frontend->>WS: WebSocket连接请求
    WS->>WS: 连接验证和会话创建
    WS->>WS: 更新连接统计
    
    WS->>Coord: 检查当前状态
    Coord->>State: get_current_state()
    State-->>Coord: 返回IDLE状态
    
    WS->>Coord: handle_frontend_connection()
    Coord->>IdleHandler: handle_frontend_connection(context)
    IdleHandler->>IdleHandler: 处理前端连接逻辑
    IdleHandler->>IdleHandler: 封装SystemStateData
    IdleHandler-->>Coord: 返回system_init消息
    
    Coord->>WS: send_state_message(SYSTEM_INIT)
    WS->>Frontend: 发送系统初始化消息
    
    Coord->>State: transition_to(FRONTEND_CONNECTED)
    State->>State: IDLE → WAITING状态转换
    State-->>Coord: 状态转换成功
    
    WS-->>Frontend: 连接建立完成
```

### 3.2 前端断开处理流程

```mermaid
sequenceDiagram
    participant Frontend as 前端应用
    participant WS as WebSocket处理器
    participant Coord as 系统协调器
    participant State as 状态管理器
    participant Components as 相关组件
    
    Note over Frontend,Components: 前端断开处理流程
    
    Frontend->>WS: 断开连接/页面刷新
    WS->>WS: 检测连接断开
    WS->>WS: 更新连接统计
    
    alt 无活跃连接
        WS->>Coord: handle_frontend_disconnect()
        Coord->>State: get_current_state()
        State-->>Coord: 返回当前状态
        
        Coord->>State: reset_to_idle()
        State->>State: 强制重置为IDLE状态
        State-->>Coord: 状态重置完成
        
        Coord->>Coord: 清理会话上下文
        Coord->>Components: 重置组件状态
        Components-->>Coord: 组件重置完成
        
        Coord-->>WS: 断开处理完成
    end
```

## 4. 状态转换处理时序图

### 4.1 用户检测到登录流程

```mermaid
sequenceDiagram
    participant ZMQ as ZMQ接收器
    participant Coord as 系统协调器
    participant WaitingHandler as Waiting处理器
    participant UserManager as 用户管理器
    participant LoginHandler as Login处理器
    participant State as 状态管理器
    participant WS as WebSocket处理器
    participant Frontend as 前端应用
    
    Note over ZMQ,Frontend: 用户检测到登录的完整流程
    
    ZMQ->>Coord: handle_pose_data(检测到用户)
    Coord->>WaitingHandler: handle_data(pose_data)
    WaitingHandler->>WaitingHandler: 用户检测逻辑
    WaitingHandler->>WaitingHandler: 检测计数达到阈值
    
    WaitingHandler->>UserManager: 获取用户信息
    UserManager-->>WaitingHandler: 返回用户数据
    
    WaitingHandler->>WaitingHandler: 封装user_detected消息
    WaitingHandler-->>Coord: 返回状态转换结果
    
    Coord->>WS: send_state_message(USER_DETECTED)
    WS->>Frontend: 发送用户检测消息
    
    Coord->>State: transition_to(USER_DETECTED)
    State->>State: WAITING → USER_LOGIN转换
    State->>LoginHandler: enter_state(context)
    LoginHandler->>LoginHandler: 初始化登录状态
    LoginHandler->>LoginHandler: 加载用户任务
    LoginHandler->>LoginHandler: 封装login_success消息
    LoginHandler-->>State: 状态初始化完成
    State-->>Coord: 状态转换成功
    
    Coord->>WS: send_state_message(LOGIN_SUCCESS)
    WS->>Frontend: 发送登录成功消息
    Frontend->>Frontend: 更新用户界面
```

### 4.2 错误处理和状态回滚

```mermaid
sequenceDiagram
    participant Handler as 状态处理器
    participant Coord as 系统协调器
    participant State as 状态管理器
    participant WS as WebSocket处理器
    participant Frontend as 前端应用
    
    Note over Handler,Frontend: 错误处理和状态回滚流程
    
    Handler->>Handler: 处理过程中发生错误
    Handler-->>Coord: 返回错误结果
    
    Coord->>Coord: 记录错误日志
    Coord->>State: 检查是否需要状态回滚
    
    alt 严重错误需要回滚
        State->>State: 回滚到安全状态
        State-->>Coord: 状态回滚完成
        Coord->>WS: 发送错误状态消息
        WS->>Frontend: 通知状态变化
    else 轻微错误继续处理
        Coord->>WS: 发送错误提示消息
        WS->>Frontend: 显示错误提示
    end
    
    Frontend->>Frontend: 更新错误状态显示
```

## 5. 关键交互模式

### 5.1 异步消息处理模式

- **ZMQ数据接收**: 后台线程持续监听，通过回调机制触发处理
- **WebSocket通信**: 异步双向通信，支持实时数据推送
- **状态处理**: 基于事件驱动的异步状态转换

### 5.2 错误处理和恢复模式

- **分层错误处理**: 每层都有独立的错误处理机制
- **优雅降级**: 关键组件失败时的降级策略
- **自动恢复**: 临时错误的自动重试和恢复

### 5.3 性能优化模式

- **批量处理**: 合并小数据包减少处理开销
- **缓存机制**: 缓存频繁访问的数据和状态
- **异步处理**: 避免阻塞主线程的长时间操作

## 6. 时序图说明

### 6.1 图例说明

- **实线箭头**: 同步调用，等待返回结果
- **虚线箭头**: 异步调用或返回结果
- **Note框**: 重要的处理阶段或说明
- **Alt框**: 条件分支处理逻辑

### 6.2 关键时间点

- **系统启动**: 约5-10秒完成所有组件初始化
- **数据处理**: 单次姿态数据处理<50ms
- **状态转换**: 状态转换处理<100ms
- **WebSocket通信**: 端到端延迟<20ms

## 相关文档

- [系统整体架构图](./architecture_overview.md)
- [详细数据流图](./data_flow_diagram.md)
- [状态机转换图](./state_machine_diagram.md)
- [组件依赖关系图](./component_dependencies.md)
