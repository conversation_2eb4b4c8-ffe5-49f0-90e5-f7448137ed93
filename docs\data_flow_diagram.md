# 智能康复系统详细数据流图

## 概述

本文档详细描述了智能康复系统中数据从外部数据源到前端用户界面的完整流转过程，包括数据格式转换、处理节点、通信协议和状态管理等关键环节。

## 完整数据流架构

```mermaid
graph TB
    subgraph "外部数据源"
        A1[姿态检测算法模块<br/>MediaPipe/OpenPose]
        A2[摄像头数据采集<br/>OpenCV Camera]
        A3[传感器数据<br/>IMU/深度传感器]
    end
    
    subgraph "数据格式化层"
        B1[ZMQDetectData<br/>姿态数据格式化]
        B2[ZMQCameraFrame<br/>视频帧格式化]
    end
    
    subgraph "ZMQ通信层"
        C1[ZMQ Publisher<br/>端口6070<br/>检测数据发布]
        C2[ZMQ Publisher<br/>端口6080<br/>摄像头数据发布]
    end
    
    subgraph "后端数据接收层"
        D1[ZMQReceiver<br/>数据接收器]
        D2[数据验证器<br/>Schema Validator]
        D3[数据解析器<br/>Data Parser]
    end
    
    subgraph "系统协调层"
        E1[SystemCoordinator<br/>系统协调器]
        E2[数据流监控器<br/>Data Flow Monitor]
    end
    
    subgraph "状态处理层"
        F1[StateManager<br/>状态管理器]
        F2[当前状态处理器<br/>Current State Handler]
        F3[业务逻辑处理<br/>Business Logic]
        F4[状态数据封装<br/>SystemStateData]
    end
    
    subgraph "消息生成层"
        G1[WebSocket消息<br/>Message Generation]
        G2[消息类型<br/>MessageType Enum]
        G3[数据序列化<br/>Data Serialization]
    end
    
    subgraph "通信传输层"
        H1[WebSocketHandler<br/>WebSocket处理器]
        H2[SocketIO服务<br/>Socket.IO Server]
        H3[网络传输<br/>Network Transport]
    end
    
    subgraph "前端接收层"
        I1[WebSocket客户端<br/>Socket.IO Client]
        I2[消息路由器<br/>Message Router]
        I3[数据反序列化<br/>Data Deserialization]
    end
    
    subgraph "前端状态管理"
        J1[Pinia Main Store<br/>主状态管理]
        J2[状态更新<br/>State Update]
        J3[响应式数据<br/>Reactive Data]
    end
    
    subgraph "UI渲染层"
        K1[Vue组件<br/>Vue Components]
        K2[Element Plus<br/>UI Components]
        K3[用户界面<br/>User Interface]
    end
    
    %% 数据流连接
    A1 --> B1
    A2 --> B2
    A3 --> B1
    
    B1 --> C1
    B2 --> C2
    
    C1 --> D1
    C2 --> D1
    
    D1 --> D2
    D2 --> D3
    D3 --> E1
    
    E1 --> E2
    E1 --> F1
    F1 --> F2
    F2 --> F3
    F3 --> F4
    
    F4 --> G1
    G1 --> G2
    G2 --> G3
    
    G3 --> H1
    H1 --> H2
    H2 --> H3
    
    H3 --> I1
    I1 --> I2
    I2 --> I3
    
    I3 --> J1
    J1 --> J2
    J2 --> J3
    
    J3 --> K1
    K1 --> K2
    K2 --> K3
    
    %% 样式定义
    classDef external fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef format fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef communication fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef backend fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef frontend fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    
    class A1,A2,A3 external
    class B1,B2 format
    class C1,C2,H1,H2,H3,I1 communication
    class D1,D2,D3,E1,E2,F1,F2,F3,F4,G1,G2,G3 backend
    class I2,I3,J1,J2,J3,K1,K2,K3 frontend
```

## 关键数据格式

### 1. ZMQ数据格式

#### ZMQDetectData (姿态检测数据)
```python
@dataclass
class ZMQDetectData:
    timestamp: float                    # 时间戳
    patient_id: str                     # 患者ID
    pose_keypoints: List[List[float]]   # 姿态关键点 [[x,y,confidence], ...]
    pose_bbox: List[float]              # 姿态边界框 [x, y, width, height]
    face_bbox: List[float]              # 面部边界框 [x, y, width, height]
```

#### ZMQCameraFrame (摄像头帧数据)
```python
@dataclass
class ZMQCameraFrame:
    timestamp: float                    # 时间戳
    frame_data: bytes                   # 图像帧数据
    frame_format: str                   # 图像格式 (JPEG/PNG)
    resolution: Tuple[int, int]         # 分辨率 (width, height)
```

### 2. 系统状态数据格式

#### SystemStateData (系统状态数据)
```python
@dataclass
class SystemStateData:
    current_state: str                  # 当前状态
    message: str                        # 状态消息
    user_info: Optional[UserInfo]       # 用户信息
    current_action: Optional[ActionInfo] # 当前动作
    action_list: List[ActionInfo]       # 动作列表
    action_templates: Dict[str, Any]    # 动作模板
    statistics: Optional[Dict[str, Any]] # 统计信息
    session_id: Optional[str]           # 会话ID
    state_history: List[str]            # 状态历史
    progress_info: Optional[Dict[str, Any]] # 进度信息
```

### 3. WebSocket消息格式

#### 标准消息结构
```json
{
    "message_type": "user_detected",
    "session_id": "session_123",
    "timestamp": **********.123,
    "data": {
        "current_state": "USER_LOGIN",
        "message": "检测到用户: P001",
        "user_info": {
            "patient_id": "P001",
            "name": "张三",
            "age": 45
        },
        "progress_info": {
            "detection_count": 3,
            "detection_threshold": 3
        }
    }
}
```

## 详细数据处理流程

### 1. 数据采集阶段

```mermaid
sequenceDiagram
    participant Algo as 姿态检测算法
    participant Camera as 摄像头
    participant Format as 数据格式化
    participant ZMQ as ZMQ发布器
    
    Algo->>Format: 姿态关键点数据
    Camera->>Format: 视频帧数据
    Format->>Format: 数据验证和格式化
    Format->>ZMQ: ZMQDetectData
    Format->>ZMQ: ZMQCameraFrame
    ZMQ->>ZMQ: 数据发布到指定端口
```

### 2. 后端数据处理阶段

```mermaid
sequenceDiagram
    participant ZMQ as ZMQ接收器
    participant Coord as 系统协调器
    participant State as 状态管理器
    participant Handler as 状态处理器
    participant WS as WebSocket处理器
    
    ZMQ->>ZMQ: 接收并验证数据
    ZMQ->>Coord: handle_pose_data()
    Coord->>State: 获取当前状态
    State-->>Coord: 当前状态信息
    Coord->>Handler: handle_data(pose_data)
    Handler->>Handler: 业务逻辑处理
    Handler-->>Coord: 处理结果+状态数据
    Coord->>WS: send_state_message()
    WS->>WS: 消息序列化和发送
```

### 3. 前端数据处理阶段

```mermaid
sequenceDiagram
    participant WS as WebSocket客户端
    participant Router as 消息路由器
    participant Store as Pinia Store
    participant Component as Vue组件
    participant UI as 用户界面
    
    WS->>Router: 接收WebSocket消息
    Router->>Router: 消息类型路由
    Router->>Store: 更新状态数据
    Store->>Store: 响应式数据更新
    Store->>Component: 状态变化通知
    Component->>UI: UI重新渲染
```

## 数据流监控点

### 1. 性能监控指标

- **数据接收频率**: ZMQ数据接收的频率和延迟
- **处理时间**: 每个处理节点的耗时统计
- **消息传输延迟**: WebSocket消息的端到端延迟
- **状态转换频率**: 系统状态转换的频率和模式

### 2. 数据质量监控

- **数据完整性**: 检查数据字段的完整性
- **数据有效性**: 验证数据值的合理性
- **消息格式**: 确保消息格式符合规范
- **序列化正确性**: 验证数据序列化和反序列化

### 3. 错误处理机制

```mermaid
graph LR
    A[数据接收] --> B{数据验证}
    B -->|通过| C[正常处理]
    B -->|失败| D[错误记录]
    D --> E[错误恢复]
    E --> F[重试机制]
    F --> A
    
    C --> G{处理成功}
    G -->|成功| H[发送结果]
    G -->|失败| I[异常处理]
    I --> J[状态回滚]
    J --> K[错误通知]
```

## 数据流优化策略

### 1. 性能优化

- **数据压缩**: 对大数据量进行压缩传输
- **批量处理**: 合并小数据包减少网络开销
- **缓存机制**: 缓存频繁访问的数据
- **异步处理**: 使用异步处理避免阻塞

### 2. 可靠性保障

- **消息确认**: 实现消息接收确认机制
- **重试机制**: 失败时自动重试
- **降级策略**: 系统异常时的降级处理
- **数据备份**: 关键数据的备份和恢复

### 3. 扩展性设计

- **水平扩展**: 支持多实例部署
- **负载均衡**: 数据处理的负载分配
- **模块化**: 独立的数据处理模块
- **插件化**: 支持自定义数据处理插件

## 相关文档

- [系统整体架构图](./architecture_overview.md)
- [组件交互时序图](./component_interaction.md)
- [状态机转换图](./state_machine_diagram.md)
- [WebSocket消息格式规范](./websocket_message_format.md)
