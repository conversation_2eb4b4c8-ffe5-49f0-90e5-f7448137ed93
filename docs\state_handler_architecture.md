# 状态处理器架构设计

## 概述

本文档描述了智能康复系统中状态处理器的新架构设计，其中每个状态处理器负责封装SystemStateData并返回给回调函数，实现了更清晰的职责分离。

## 架构原则

### 1. 职责分离

- **状态处理器 (Handler)**: 负责业务逻辑处理和数据封装
- **系统协调器 (Coordinator)**: 负责状态转换和消息发送
- **WebSocket处理器**: 负责消息序列化和网络传输

### 2. 数据封装

每个状态处理器必须返回包含以下字段的结果：

```python
{
    "success": bool,                    # 处理是否成功
    "trigger_event": StateTransitionEvent,  # 触发的状态转换事件
    "next_state": SystemState,          # 目标状态
    "websocket_message": MessageType,   # WebSocket消息类型
    "state_data": SystemStateData       # 封装好的状态数据
}
```

## 实现模式

### 状态处理器实现模式

```python
class ExampleStateHandler:
    def handle_data(self, data, context) -> Dict[str, Any]:
        try:
            # 1. 业务逻辑处理
            result = self._process_business_logic(data, context)
            
            if result.success:
                # 2. 封装SystemStateData
                state_data = SystemStateData(
                    current_state=target_state,
                    message="处理成功消息",
                    user_info=user_info,
                    current_action=current_action,
                    # ... 其他字段
                )
                
                # 3. 添加特定信息到progress_info
                state_data.progress_info = {
                    "timestamp": time.time(),
                    "processing_type": "example_processing",
                    # ... 其他进度信息
                }
                
                # 4. 返回标准格式
                return {
                    "success": True,
                    "trigger_event": StateTransitionEvent.EXAMPLE_EVENT,
                    "next_state": SystemState.NEXT_STATE,
                    "websocket_message": MessageType.EXAMPLE_MESSAGE,
                    "state_data": state_data
                }
            else:
                # 处理失败的情况
                return {
                    "success": False,
                    "message": "处理失败原因"
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"处理异常: {e}"
            }
```

### 系统协调器处理模式

```python
def _send_data_result(self, data_type: str, result: Dict[str, Any]):
    """发送数据处理结果"""
    try:
        if self.websocket_handler and result.get("success"):
            # 检查状态转换
            trigger_event = result.get("trigger_event")
            if trigger_event:
                # 执行状态转换
                self.state_manager.transition_to(trigger_event)
            
            # 发送WebSocket消息
            websocket_message = result.get("websocket_message")
            state_data = result.get("state_data")
            
            if websocket_message and state_data:
                # 直接使用handler封装好的SystemStateData
                self.websocket_handler.send_state_message(websocket_message, state_data)
                self.logger.info(f"发送状态消息: {websocket_message.value}")
                
    except Exception as e:
        self.logger.error(f"数据结果处理失败: {e}")
```

## 具体实现示例

### 1. 用户检测处理器 (WaitingHandler)

**功能**: 检测用户并触发登录流程

**返回数据**:
```python
state_data = SystemStateData(
    current_state=SystemState.USER_LOGIN,
    message=f"检测到用户: {patient_id}",
    progress_info={
        "patient_id": patient_id,
        "detection_timestamp": pose_data.timestamp,
        "confidence": 1.0,
        "detection_type": "pose_based"
    }
)

return {
    "success": True,
    "trigger_event": StateTransitionEvent.USER_DETECTED,
    "next_state": SystemState.USER_LOGIN,
    "websocket_message": MessageType.USER_DETECTED,
    "state_data": state_data
}
```

### 2. 用户登录处理器 (UserLoginHandler)

**功能**: 验证用户身份并加载任务

**返回数据**:
```python
state_data = SystemStateData(
    current_state=SystemState.ACTION_PREPARATION,
    message=f"欢迎用户 {user_info.name}，任务已加载",
    user_info=user_info,
    current_action=current_action,
    action_list=action_list,
    progress_info={
        "login_timestamp": time.time(),
        "login_type": "user_verification",
        "tasks_loaded": len(action_list)
    }
)

return {
    "success": True,
    "trigger_event": StateTransitionEvent.USER_VERIFIED_AND_TASKS_LOADED,
    "next_state": SystemState.ACTION_PREPARATION,
    "websocket_message": MessageType.LOGIN_SUCCESS,
    "state_data": state_data
}
```

### 3. 训练准备处理器 (ActionPreparationHandler)

**功能**: 准备训练动作并开始训练

**返回数据**:
```python
state_data = SystemStateData(
    current_state=SystemState.ACTION_TRAINING,
    message=f"开始训练: {action_type}",
    user_info=user_info,
    current_action=current_action,
    progress_info={
        "preparation_timestamp": time.time(),
        "action_type": action_type,
        "target_sets": target_sets,
        "target_reps": target_reps
    }
)

return {
    "success": True,
    "trigger_event": StateTransitionEvent.TRAINING_STARTED,
    "next_state": SystemState.ACTION_TRAINING,
    "websocket_message": MessageType.ACTION_READY,
    "state_data": state_data
}
```

## 前端处理

前端根据不同的message_type处理相应的状态：

```javascript
// 用户检测消息处理
websocketService.subscribe('user_detected', (message) => {
  const stateData = message.data
  const progressInfo = stateData.progress_info
  
  // 显示检测到用户的动画
  showUserDetectionAnimation(progressInfo.patient_id)
})

// 登录成功消息处理
websocketService.subscribe('login_success', (message) => {
  const stateData = message.data
  const userInfo = stateData.user_info
  
  // 显示欢迎信息
  showWelcomeMessage(userInfo.name)
  
  // 3秒后跳转到训练页面
  setTimeout(() => {
    router.push('/training')
  }, 3000)
})

// 训练准备消息处理
websocketService.subscribe('action_ready', (message) => {
  const stateData = message.data
  const currentAction = stateData.current_action
  
  // 显示训练准备界面
  showTrainingPreparation(currentAction)
})
```

## 优势

### 1. 清晰的职责分离
- 每个处理器只负责自己的业务逻辑和数据封装
- 系统协调器只负责状态转换和消息分发
- WebSocket处理器只负责网络通信

### 2. 统一的数据格式
- 所有状态数据都使用SystemStateData格式
- 前端可以统一处理不同类型的状态消息
- 便于扩展和维护

### 3. 易于测试和调试
- 每个处理器可以独立测试
- 数据流向清晰，便于调试
- 状态转换逻辑集中管理

### 4. 高度可扩展
- 添加新状态只需要实现对应的处理器
- 新的消息类型可以轻松集成
- 业务逻辑变更不影响其他组件

## 注意事项

1. **数据完整性**: 确保SystemStateData包含前端所需的所有信息
2. **错误处理**: 每个处理器都要有完善的异常处理
3. **性能考虑**: 避免在处理器中进行耗时操作
4. **状态一致性**: 确保状态转换的原子性和一致性

## 扩展指南

添加新的状态处理器时，请遵循以下步骤：

1. 创建新的处理器类
2. 实现业务逻辑处理方法
3. 封装SystemStateData
4. 返回标准格式的结果
5. 在系统协调器中注册处理器
6. 添加对应的前端消息监听器
7. 编写单元测试
