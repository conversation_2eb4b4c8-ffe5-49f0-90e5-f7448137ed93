# 智能康复系统状态机转换图

## 概述

智能康复系统采用状态机驱动的架构模式，通过明确定义的状态和事件来管理整个康复训练流程。本文档详细描述了系统的7个核心状态、状态转换条件和触发事件。

## 核心状态机转换图

```mermaid
stateDiagram-v2
    [*] --> IDLE
    
    IDLE --> WAITING : FRONTEND_CONNECTED
    WAITING --> USER_LOGIN : USER_DETECTED
    USER_LOGIN --> ACTION_PREPARATION : LOGIN_SUCCESS
    ACTION_PREPARATION --> ACTION_TRAINING : PREPARATION_COMPLETED
    ACTION_TRAINING --> ACTION_REST : ACTION_COMPLETED
    ACTION_TRAINING --> ACTION_COMPLETED : TRAINING_FINISHED
    ACTION_REST --> ACTION_TRAINING : REST_COMPLETED
    ACTION_REST --> ACTION_COMPLETED : TRAINING_FINISHED
    ACTION_COMPLETED --> ACTION_PREPARATION : NEXT_ACTION
    ACTION_COMPLETED --> WAITING : USER_LEFT
    
    %% 错误处理转换
    USER_LOGIN --> WAITING : ERROR_OCCURRED
    ACTION_PREPARATION --> WAITING : ERROR_OCCURRED
    ACTION_TRAINING --> WAITING : ERROR_OCCURRED
    ACTION_REST --> WAITING : ERROR_OCCURRED
    
    %% 前端断开处理
    WAITING --> IDLE : FRONTEND_DISCONNECTED
    USER_LOGIN --> IDLE : FRONTEND_DISCONNECTED
    ACTION_PREPARATION --> IDLE : FRONTEND_DISCONNECTED
    ACTION_TRAINING --> IDLE : FRONTEND_DISCONNECTED
    ACTION_REST --> IDLE : FRONTEND_DISCONNECTED
    ACTION_COMPLETED --> IDLE : FRONTEND_DISCONNECTED
    
    %% 状态说明
    IDLE : 系统空闲状态
    IDLE : 等待前端连接
    
    WAITING : 等待用户状态
    WAITING : 检测用户进入
    
    USER_LOGIN : 用户登录状态
    USER_LOGIN : 用户身份验证
    
    ACTION_PREPARATION : 动作准备状态
    ACTION_PREPARATION : 训练前准备
    
    ACTION_TRAINING : 动作训练状态
    ACTION_TRAINING : 执行康复训练
    
    ACTION_REST : 动作休息状态
    ACTION_REST : 训练间隔休息
    
    ACTION_COMPLETED : 动作完成状态
    ACTION_COMPLETED : 训练结果展示
```

## 状态详细说明

### 1. IDLE (系统空闲状态)

**状态描述**: 系统初始状态，等待前端应用连接

**主要特征**:
- 系统已启动但无活跃用户
- 所有组件处于待机状态
- 等待WebSocket连接建立

**进入条件**:
- 系统启动完成
- 前端断开连接后的重置

**退出条件**:
- 前端应用成功连接 → WAITING

**状态处理器**: `IdleHandler`

### 2. WAITING (等待用户状态)

**状态描述**: 前端已连接，等待检测到用户进入

**主要特征**:
- 摄像头开始工作
- 姿态检测算法运行
- 持续监测用户出现

**进入条件**:
- 前端连接建立成功
- 错误恢复后的重置
- 用户离开后的重置

**退出条件**:
- 检测到用户 → USER_LOGIN
- 前端断开连接 → IDLE

**状态处理器**: `WaitingHandler`

**关键逻辑**:
- 用户检测计数机制
- 检测阈值达到后触发状态转换

### 3. USER_LOGIN (用户登录状态)

**状态描述**: 用户身份验证和信息加载

**主要特征**:
- 用户身份识别
- 加载用户训练计划
- 准备训练环境

**进入条件**:
- 用户检测成功

**退出条件**:
- 登录成功 → ACTION_PREPARATION
- 登录失败/错误 → WAITING
- 前端断开连接 → IDLE

**状态处理器**: `UserLoginHandler`

**关键逻辑**:
- 用户信息验证
- 训练任务加载
- 系统环境初始化

### 4. ACTION_PREPARATION (动作准备状态)

**状态描述**: 训练前的准备阶段

**主要特征**:
- 显示训练指导
- 用户姿态调整
- 确认训练开始

**进入条件**:
- 用户登录成功
- 完成一个动作后继续下一个

**退出条件**:
- 准备完成 → ACTION_TRAINING
- 准备失败/错误 → WAITING
- 前端断开连接 → IDLE

**状态处理器**: `ActionPreparationHandler`

**关键逻辑**:
- 动作指导展示
- 用户准备状态检测
- 训练参数设置

### 5. ACTION_TRAINING (动作训练状态)

**状态描述**: 正在执行康复训练动作

**主要特征**:
- 实时姿态分析
- 动作质量评估
- 训练进度跟踪

**进入条件**:
- 训练准备完成
- 休息结束继续训练

**退出条件**:
- 单个动作完成 → ACTION_REST
- 所有训练完成 → ACTION_COMPLETED
- 训练失败/错误 → WAITING
- 前端断开连接 → IDLE

**状态处理器**: `ActionTrainingHandler`

**关键逻辑**:
- 实时姿态评估
- 训练质量打分
- 进度统计更新

### 6. ACTION_REST (动作休息状态)

**状态描述**: 训练动作间的休息阶段

**主要特征**:
- 显示休息倒计时
- 展示训练结果
- 准备下一个动作

**进入条件**:
- 单个训练动作完成

**退出条件**:
- 休息结束 → ACTION_TRAINING
- 所有训练完成 → ACTION_COMPLETED
- 休息失败/错误 → WAITING
- 前端断开连接 → IDLE

**状态处理器**: `ActionRestHandler`

**关键逻辑**:
- 休息时间管理
- 训练结果展示
- 下一动作准备

### 7. ACTION_COMPLETED (动作完成状态)

**状态描述**: 训练会话完成，展示最终结果

**主要特征**:
- 显示训练总结
- 生成训练报告
- 等待用户操作

**进入条件**:
- 所有训练动作完成

**退出条件**:
- 开始新训练 → ACTION_PREPARATION
- 用户离开 → WAITING
- 前端断开连接 → IDLE

**状态处理器**: `ActionCompletedHandler`

**关键逻辑**:
- 训练数据汇总
- 报告生成
- 用户选择处理

## 状态转换事件

### 正常流程事件

| 事件名称 | 触发条件 | 源状态 | 目标状态 |
|---------|---------|--------|---------|
| FRONTEND_CONNECTED | 前端WebSocket连接成功 | IDLE | WAITING |
| USER_DETECTED | 检测到用户进入 | WAITING | USER_LOGIN |
| LOGIN_SUCCESS | 用户登录验证成功 | USER_LOGIN | ACTION_PREPARATION |
| PREPARATION_COMPLETED | 训练准备完成 | ACTION_PREPARATION | ACTION_TRAINING |
| ACTION_COMPLETED | 单个动作训练完成 | ACTION_TRAINING | ACTION_REST |
| REST_COMPLETED | 休息时间结束 | ACTION_REST | ACTION_TRAINING |
| TRAINING_FINISHED | 所有训练完成 | ACTION_TRAINING/ACTION_REST | ACTION_COMPLETED |
| NEXT_ACTION | 开始新的训练 | ACTION_COMPLETED | ACTION_PREPARATION |
| USER_LEFT | 用户离开检测区域 | ACTION_COMPLETED | WAITING |

### 异常处理事件

| 事件名称 | 触发条件 | 源状态 | 目标状态 |
|---------|---------|--------|---------|
| ERROR_OCCURRED | 处理过程中发生错误 | USER_LOGIN/ACTION_PREPARATION/ACTION_TRAINING/ACTION_REST | WAITING |
| FRONTEND_DISCONNECTED | 前端连接断开 | 除IDLE外的所有状态 | IDLE |

## 状态转换规则

### 1. 单向转换规则
- 正常业务流程按照预定义路径进行
- 不允许跳跃式状态转换
- 每个状态转换都有明确的触发条件

### 2. 错误恢复规则
- 任何业务状态发生错误都回退到WAITING状态
- 前端断开连接强制回到IDLE状态
- 错误状态下保留必要的上下文信息

### 3. 并发处理规则
- 同一时间只能处于一个状态
- 状态转换是原子操作
- 状态转换过程中拒绝其他转换请求

## 状态持久化

### 状态数据结构
```python
@dataclass
class SystemStateData:
    current_state: str                  # 当前状态
    previous_state: Optional[str]       # 前一个状态
    state_entry_time: float            # 状态进入时间
    state_context: Dict[str, Any]      # 状态上下文数据
    transition_history: List[str]      # 状态转换历史
```

### 状态恢复机制
- 系统重启后自动恢复到IDLE状态
- 前端重连后根据上下文恢复到合适状态
- 异常中断后的状态恢复和数据保护

## 性能指标

### 状态转换性能
- **状态转换延迟**: < 100ms
- **状态处理器初始化**: < 50ms
- **状态数据序列化**: < 10ms
- **WebSocket消息发送**: < 20ms

### 状态稳定性
- **状态转换成功率**: > 99.9%
- **错误恢复时间**: < 2s
- **状态一致性**: 100%

## 相关文档

- [系统整体架构图](./architecture_overview.md)
- [详细数据流图](./data_flow_diagram.md)
- [组件交互时序图](./component_interaction.md)
- [组件依赖关系图](./component_dependencies.md)
