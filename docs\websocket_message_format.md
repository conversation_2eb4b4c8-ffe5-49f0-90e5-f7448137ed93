# WebSocket消息格式规范

## 概述

本文档定义了智能康复系统中前后端WebSocket通信的统一消息格式，确保所有消息都包含必要的session_id和标准化的数据结构。

## 统一消息格式

### 基本消息结构

所有WebSocket消息都遵循以下统一格式：

```json
{
  "message_type": "string",     // 消息类型（必需）
  "session_id": "string",       // 会话ID（必需）
  "timestamp": **********.123,  // 时间戳（必需）
  "data": {}                    // 消息数据（必需）
}
```

### 字段说明

- **message_type**: 消息类型，使用MessageType枚举值
- **session_id**: 唯一会话标识符，用于跟踪客户端会话
- **timestamp**: Unix时间戳（秒，包含小数部分）
- **data**: 具体的消息数据，根据消息类型而变化

## 支持的消息类型

### 1. 系统状态消息 (system_state)

**用途**: 通知前端系统状态变化

**消息格式**:
```json
{
  "message_type": "system_state",
  "session_id": "uuid-string",
  "timestamp": **********.123,
  "data": {
    "current_state": "WAITING",
    "message": "系统当前状态: WAITING"
  }
}
```

### 2. 用户检测消息 (user_detected)

**用途**: 通知前端检测到用户

**消息格式**:
```json
{
  "message_type": "user_detected",
  "session_id": "uuid-string",
  "timestamp": **********.123,
  "data": {
    "current_state": "USER_LOGIN",
    "message": "检测到用户",
    "user_info": null,
    "current_action": null,
    "action_list": [],
    "action_templates": {},
    "progress_info": {
      "patient_id": "user_001",
      "detection_timestamp": **********.123,
      "confidence": 1.0,
      "detection_type": "pose_based"
    },
    "statistics": null,
    "session_id": null,
    "state_history": []
  }
}
```

**数据说明**:
- `data`: SystemStateData格式，包含完整的系统状态信息
- `progress_info`: 检测相关的详细信息，包含patient_id、时间戳、置信度等

### 3. 登录成功消息 (login_success)

**用途**: 通知前端用户登录成功

**消息格式**:
```json
{
  "message_type": "login_success",
  "session_id": "uuid-string",
  "timestamp": **********.123,
  "data": {
    "user_info": {
      "patient_id": "user_001",
      "name": "张三",
      "age": 65,
      "gender": "male"
    },
    "current_action": {
      "action_type": "shoulder_touch",
      "side": "left",
      "sets": 3,
      "reps_per_set": 10,
      "target_score": 80
    }
  }
}
```

### 4. 训练信息消息 (training_info)

**用途**: 发送训练过程中的实时信息

**消息格式**:
```json
{
  "message_type": "training_info",
  "session_id": "uuid-string",
  "timestamp": **********.123,
  "data": {
    "current_score": 85,
    "rep_count": 5,
    "set_count": 2,
    "feedback": "动作标准，继续保持"
  }
}
```

## Session管理

### Session ID生成

- 每个WebSocket连接建立时，后端自动生成唯一的UUID作为session_id
- Session ID在连接期间保持不变
- 断开连接时清理session映射

### Session生命周期

1. **连接建立**: 生成新的session_id，建立client_id到session_id的映射
2. **消息发送**: 所有消息都包含对应的session_id
3. **连接断开**: 清理session映射和相关数据

## 前端处理

### 消息接收

前端应该验证接收到的消息格式：

```javascript
websocketService.subscribe('system_state', (message) => {
  // 验证消息格式
  if (!message.session_id || !message.timestamp || !message.data) {
    console.error('无效的消息格式:', message)
    return
  }
  
  // 处理session_id
  if (message.session_id && !sessionId.value) {
    sessionId.value = message.session_id
    sessionStartTime.value = new Date()
  }
  
  // 处理消息数据
  if (message.data && message.data.current_state) {
    updateSystemState(message.data.current_state, message.data.message)
  }
})
```

### Session状态管理

前端应该维护session相关状态：

```javascript
const sessionId = ref(null)
const sessionStartTime = ref(null)

// 在系统重置时清理session信息
function resetSystemState() {
  sessionId.value = null
  sessionStartTime.value = null
  // ... 其他重置逻辑
}
```

## 后端实现

### 消息发送

使用新的状态消息发送方法：

```python
def send_state_message(self, message_type: MessageType, state_data: SystemStateData, target_client_id: Optional[str] = None):
    """发送状态消息给特定客户端"""
    # 获取session_id
    session_id = None
    if target_client_id and target_client_id in self.client_sessions:
        session_id = self.client_sessions[target_client_id]

    # 创建WebSocket消息，数据使用SystemStateData格式
    message = WebSocketMessage(
        message_type=message_type.value,
        session_id=session_id,
        timestamp=time.time(),
        data=state_data  # 统一使用SystemStateData
    )

    # 序列化并发送消息给特定客户端
    message_dict = DataSerializer.websocket_message_to_dict(message)
    self.socketio.emit(message_type.value, message_dict, to=target_client_id)
```

**关键改进**：
- 不再使用广播模式，而是发送给特定客户端
- 数据统一使用SystemStateData格式封装
- 使用DataSerializer.websocket_message_to_dict序列化
- 发送格式：`self.socketio.emit(message_type.value, message_dict, to=target_client_id)`

### Session管理

```python
# 连接时生成session_id
session_id = str(uuid.uuid4())
self.client_sessions[client_id] = session_id

# 断开时清理session
if client_id in self.client_sessions:
    del self.client_sessions[client_id]
```

## 错误处理

### 消息验证失败

- 前端收到格式不正确的消息时，应记录错误并忽略该消息
- 后端发送消息失败时，应记录错误日志

### Session丢失

- 如果前端检测到session_id丢失，应该重新连接
- 后端应该能够处理session_id不存在的情况

## 测试验证

使用提供的测试用例验证消息格式的一致性：

```bash
cd backend
python -m pytest tests/test_websocket_format.py -v
```

## 注意事项

1. **向后兼容性**: 新的消息格式保持向后兼容
2. **性能考虑**: session_id查找使用字典映射，保证O(1)时间复杂度
3. **安全性**: session_id使用UUID4生成，确保唯一性和安全性
4. **调试支持**: 所有消息都包含timestamp，便于调试和日志分析

## 更新日志

- **v1.0**: 初始版本，定义基本消息格式
- **v1.1**: 添加session_id支持，统一前后端消息格式
