<template>
  <div id="app" class="h-screen overflow-hidden">
    <!-- 页面过渡动画 -->
    <router-view v-slot="{ Component, route }">
      <transition
        :name="transitionName"
        :mode="transitionMode"
        appear
      >
        <component
          :is="Component"
          :key="route.path"
          class="h-full"
        />
      </transition>
    </router-view>

    <!-- 全局加载指示器 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-content">
        <el-icon class="loading-icon" :size="40">
          <Loading />
        </el-icon>
        <p class="loading-text">{{ loadingText }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted } from 'vue'
import { usePageTransition } from '@/utils/useRouter'
import { useMainStore } from '@/stores/main'
import { Loading } from '@element-plus/icons-vue'

// 页面过渡
const { transitionName, transitionMode } = usePageTransition()

// 系统状态
const mainStore = useMainStore()
const isConnecting = computed(() => false) // 简化实现，主store不需要isConnecting状态

// 加载状态
const isLoading = computed(() => {
  return isConnecting.value
})

const loadingText = computed(() => {
  if (isConnecting.value) return '正在连接系统...'
  return '加载中...'
})

// 生命周期
onMounted(() => {
  console.log('智能康复系统应用启动')
})
</script>

<style scoped>
/* 页面过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-left-enter-active,
.slide-left-leave-active {
  transition: all 0.4s ease;
}

.slide-left-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.slide-left-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.4s ease;
}

.slide-up-enter-from {
  transform: translateY(100%);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(-100%);
  opacity: 0;
}

/* 全局加载指示器 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.loading-icon {
  color: #409eff;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.loading-text {
  color: #606266;
  font-size: 14px;
  margin: 0;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 确保页面组件占满高度 */
.h-full {
  height: 100%;
}
</style>
