<template>
  <el-card shadow="never" class="feedback-panel-card">
    <template #header>
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <el-icon class="text-lg text-purple-500 mr-2">
            <ChatDotRound />
          </el-icon>
          <span class="font-semibold text-gray-700">实时反馈</span>
        </div>
        <div class="flex items-center">
          <div 
            :class="[
              'w-3 h-3 rounded-full mr-2',
              getStateColor(currentState)
            ]"
          ></div>
          <span class="text-sm font-medium" :class="getStateTextColor(currentState)">
            {{ getStateText(currentState) }}
          </span>
        </div>
      </div>
    </template>

    <div class="space-y-4">
      <!-- 主要反馈消息区域 -->
      <div class="bg-gray-50 p-4 rounded-lg min-h-[120px]">
        <div v-if="!hasRecentFeedback && !message" class="text-center py-6 text-gray-500">
          <el-icon class="text-3xl mb-2">
            <MessageBox />
          </el-icon>
          <p class="text-sm">等待系统反馈...</p>
        </div>

        <!-- 系统消息 -->
        <div v-if="message" class="mb-3">
          <div class="flex items-start">
            <el-icon class="text-blue-500 mr-2 mt-1">
              <Bell />
            </el-icon>
            <div class="flex-1">
              <div class="text-sm font-medium text-gray-700 mb-1">系统消息</div>
              <p class="text-sm text-gray-600">{{ message }}</p>
            </div>
          </div>
        </div>

        <!-- 实时反馈消息列表 -->
        <div v-if="hasRecentFeedback" class="space-y-2">
          <div class="text-sm font-medium text-gray-700 mb-2 flex items-center">
            <el-icon class="text-green-500 mr-1">
              <ChatLineRound />
            </el-icon>
            训练反馈
          </div>
          <div class="space-y-1">
            <div 
              v-for="(feedbackMsg, index) in recentFeedback" 
              :key="index"
              class="flex items-start p-2 bg-white rounded border-l-4"
              :class="getFeedbackBorderColor(feedbackMsg)"
            >
              <el-icon class="mr-2 mt-0.5" :class="getFeedbackIconColor(feedbackMsg)">
                <component :is="getFeedbackIcon(feedbackMsg)" />
              </el-icon>
              <span class="text-sm" :class="getFeedbackTextColor(feedbackMsg)">
                {{ feedbackMsg }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 训练状态指示 -->
      <div class="grid grid-cols-2 gap-3">
        <div class="bg-blue-50 p-3 rounded-lg text-center">
          <div class="text-lg font-bold text-blue-600">
            {{ getActionStatusIcon() }}
          </div>
          <div class="text-xs text-blue-700 mt-1">
            {{ getActionStatusText() }}
          </div>
        </div>
        <div class="bg-green-50 p-3 rounded-lg text-center">
          <div class="text-lg font-bold text-green-600">
            {{ getSystemStatusIcon() }}
          </div>
          <div class="text-xs text-green-700 mt-1">
            {{ getSystemStatusText() }}
          </div>
        </div>
      </div>

      <!-- 训练指导提示 -->
      <div class="bg-gradient-to-r from-indigo-50 to-purple-50 p-3 rounded-lg border border-indigo-200">
        <div class="flex items-center mb-2">
          <el-icon class="text-indigo-500 mr-2">
            <InfoFilled />
          </el-icon>
          <span class="text-sm font-medium text-indigo-700">训练指导</span>
        </div>
        <div class="space-y-1 text-xs text-indigo-600">
          <div class="flex items-center">
            <el-icon class="mr-1">
              <Check />
            </el-icon>
            <span>保持动作标准，注意姿态正确</span>
          </div>
          <div class="flex items-center">
            <el-icon class="mr-1">
              <Check />
            </el-icon>
            <span>跟随系统节拍，保持稳定节奏</span>
          </div>
          <div class="flex items-center">
            <el-icon class="mr-1">
              <Check />
            </el-icon>
            <span>专注训练过程，听从系统指导</span>
          </div>
        </div>
      </div>


    </div>
  </el-card>
</template>

<script setup>
import { computed } from 'vue'
import {
  ChatDotRound,
  MessageBox,
  Bell,
  ChatLineRound,
  InfoFilled,
  Check,
  SuccessFilled,
  WarningFilled,
  CircleCheck
} from '@element-plus/icons-vue'

// Props定义
const props = defineProps({
  message: { 
    type: String, 
    default: '' 
  },
  currentState: { 
    type: String, 
    default: 'IDLE' 
  },
  currentAction: {
    type: Object,
    default: null
  }
})

// 计算属性
const hasRecentFeedback = computed(() => {
  return props.currentAction?.feedback_messages?.length > 0
})

const recentFeedback = computed(() => {
  if (!hasRecentFeedback.value) return []
  return props.currentAction.feedback_messages.slice(-5) // 显示最近5条反馈
})

// 得分相关计算属性已移除，简化组件

// 方法
const getStateColor = (state) => {
  switch (state) {
    case 'ACTION_TRAINING': return 'bg-green-500 animate-pulse'
    case 'ACTION_REST': return 'bg-yellow-500'
    case 'ACTION_PREPARATION': return 'bg-blue-500'
    case 'ACTION_COMPLETED': return 'bg-purple-500'
    case 'USER_LOGIN': return 'bg-indigo-500'
    case 'WAITING': return 'bg-orange-500'
    default: return 'bg-gray-400'
  }
}

const getStateTextColor = (state) => {
  switch (state) {
    case 'ACTION_TRAINING': return 'text-green-600'
    case 'ACTION_REST': return 'text-yellow-600'
    case 'ACTION_PREPARATION': return 'text-blue-600'
    case 'ACTION_COMPLETED': return 'text-purple-600'
    case 'USER_LOGIN': return 'text-indigo-600'
    case 'WAITING': return 'text-orange-600'
    default: return 'text-gray-600'
  }
}

const getStateText = (state) => {
  switch (state) {
    case 'ACTION_TRAINING': return '训练中'
    case 'ACTION_REST': return '休息中'
    case 'ACTION_PREPARATION': return '准备中'
    case 'ACTION_COMPLETED': return '已完成'
    case 'USER_LOGIN': return '用户识别'
    case 'WAITING': return '等待中'
    default: return '系统就绪'
  }
}

const getFeedbackBorderColor = (message) => {
  const msg = message.toLowerCase()
  if (msg.includes('优秀') || msg.includes('很好') || msg.includes('标准')) {
    return 'border-green-400'
  } else if (msg.includes('注意') || msg.includes('调整')) {
    return 'border-yellow-400'
  } else if (msg.includes('错误') || msg.includes('失败')) {
    return 'border-red-400'
  }
  return 'border-blue-400'
}

const getFeedbackIconColor = (message) => {
  const msg = message.toLowerCase()
  if (msg.includes('优秀') || msg.includes('很好') || msg.includes('标准')) {
    return 'text-green-500'
  } else if (msg.includes('注意') || msg.includes('调整')) {
    return 'text-yellow-500'
  } else if (msg.includes('错误') || msg.includes('失败')) {
    return 'text-red-500'
  }
  return 'text-blue-500'
}

const getFeedbackTextColor = (message) => {
  const msg = message.toLowerCase()
  if (msg.includes('优秀') || msg.includes('很好') || msg.includes('标准')) {
    return 'text-green-700'
  } else if (msg.includes('注意') || msg.includes('调整')) {
    return 'text-yellow-700'
  } else if (msg.includes('错误') || msg.includes('失败')) {
    return 'text-red-700'
  }
  return 'text-blue-700'
}

const getFeedbackIcon = (message) => {
  const msg = message.toLowerCase()
  if (msg.includes('优秀') || msg.includes('很好') || msg.includes('标准')) {
    return SuccessFilled
  } else if (msg.includes('注意') || msg.includes('调整')) {
    return WarningFilled
  }
  return CircleCheck
}

const getActionStatusIcon = () => {
  if (props.currentAction?.action_status === 'active') return '🏃'
  if (props.currentAction?.action_status === 'completed') return '✅'
  if (props.currentAction?.action_status === 'pending') return '⏳'
  return '⚪'
}

const getActionStatusText = () => {
  if (props.currentAction?.action_status === 'active') return '动作进行中'
  if (props.currentAction?.action_status === 'completed') return '动作已完成'
  if (props.currentAction?.action_status === 'pending') return '等待开始'
  return '无动作'
}

const getSystemStatusIcon = () => {
  switch (props.currentState) {
    case 'ACTION_TRAINING': return '🎯'
    case 'ACTION_REST': return '⏸️'
    case 'ACTION_PREPARATION': return '🔄'
    default: return '💤'
  }
}

const getSystemStatusText = () => {
  return getStateText(props.currentState)
}

// 得分相关方法已移除，简化组件
</script>

<style scoped>
.feedback-panel-card {
  height: 100%;
}

.feedback-panel-card :deep(.el-card__body) {
  padding: 1rem;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .grid-cols-2 {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}
</style>
