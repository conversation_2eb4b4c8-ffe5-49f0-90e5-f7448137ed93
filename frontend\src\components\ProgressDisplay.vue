<template>
  <el-card shadow="never" class="progress-display-card">
    <template #header>
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <el-icon class="text-lg text-green-500 mr-2">
            <TrendCharts />
          </el-icon>
          <span class="font-semibold text-gray-700">训练进度</span>
        </div>
        <el-tag v-if="currentAction" :type="getStatusTagType()" size="small">
          {{ getStatusText() }}
        </el-tag>
      </div>
    </template>

    <div v-if="!currentAction" class="text-center py-8 text-gray-500">
      <el-icon class="text-4xl mb-2">
        <Clock />
      </el-icon>
      <p>等待开始训练</p>
      <p class="text-sm mt-1">请选择训练任务</p>
    </div>

    <div v-else class="space-y-6">
      <!-- 总体进度 -->
      <div>
        <div class="flex justify-between items-center mb-3">
          <span class="text-sm font-medium text-gray-700">总体进度</span>
          <span class="text-lg font-bold text-blue-600">{{ overallProgress }}%</span>
        </div>
        <el-progress 
          :percentage="overallProgress" 
          :stroke-width="10"
          :show-text="false"
          :color="getProgressColor()"
          class="mb-2"
        />
        <div class="text-xs text-gray-500 text-center">
          {{ completedReps }} / {{ totalReps }} 次动作完成
        </div>
      </div>

      <!-- 当前动作信息 -->
      <div class="bg-gray-50 p-4 rounded-lg">
        <div class="flex items-center justify-between mb-3">
          <h3 class="text-lg font-semibold text-gray-800">
            {{ getActionDisplayName() }}
          </h3>
          <el-tag :type="getActionStatusType()" size="small">
            {{ getActionStatusText() }}
          </el-tag>
        </div>

        <!-- 组数和次数信息 -->
        <div class="grid grid-cols-2 gap-4 mb-4">
          <div class="text-center p-3 bg-white rounded-lg border">
            <div class="text-2xl font-bold text-blue-600">
              {{ currentAction.current_sets }}
            </div>
            <div class="text-sm text-gray-500">当前组数</div>
            <div class="text-xs text-gray-400 mt-1">
              / {{ currentAction.total_sets }} 组
            </div>
          </div>
          <div class="text-center p-3 bg-white rounded-lg border">
            <div class="text-2xl font-bold text-green-600">
              {{ currentAction.current_reps }}
            </div>
            <div class="text-sm text-gray-500">当前次数</div>
            <div class="text-xs text-gray-400 mt-1">
              / {{ currentAction.reps_per_set }} 次
            </div>
          </div>
        </div>

        <!-- 当前组进度 -->
        <div class="mb-4">
          <div class="flex justify-between text-sm mb-2">
            <span class="text-gray-600">当前组进度</span>
            <span class="font-medium">{{ currentSetProgress }}%</span>
          </div>
          <el-progress 
            :percentage="currentSetProgress" 
            :stroke-width="6"
            :show-text="false"
            color="#10b981"
          />
        </div>

        <!-- 动作得分 -->
        <div v-if="hasScores" class="mb-4">
          <div class="flex justify-between items-center">
            <span class="text-sm font-medium text-gray-700">平均得分</span>
            <span class="text-lg font-bold" :class="getScoreColor()">
              {{ averageScore }}
            </span>
          </div>
        </div>

        <!-- 休息时间 -->
        <div v-if="currentAction.rest_time > 0" class="mb-4">
          <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg border border-yellow-200">
            <div class="flex items-center">
              <el-icon class="text-yellow-500 mr-2">
                <Timer />
              </el-icon>
              <span class="text-sm font-medium text-yellow-700">休息时间</span>
            </div>
            <span class="text-lg font-bold text-yellow-600">
              {{ formatTime(currentAction.rest_time) }}
            </span>
          </div>
        </div>

        <!-- 训练时长 -->
        <div v-if="trainingDuration > 0" class="grid grid-cols-2 gap-4 text-sm">
          <div class="flex items-center">
            <el-icon class="text-gray-400 mr-2">
              <Stopwatch />
            </el-icon>
            <span class="text-gray-600">训练时长: {{ formatTime(trainingDuration) }}</span>
          </div>
          <div class="flex items-center">
            <el-icon class="text-gray-400 mr-2">
              <Flag />
            </el-icon>
            <span class="text-gray-600">
              {{ currentAction.set_completed ? '组已完成' : '组进行中' }}
            </span>
          </div>
        </div>
      </div>

      <!-- 实时反馈消息 -->
      <div v-if="hasRecentFeedback" class="bg-blue-50 p-3 rounded-lg border border-blue-200">
        <div class="flex items-center mb-2">
          <el-icon class="text-blue-500 mr-2">
            <ChatDotRound />
          </el-icon>
          <span class="text-sm font-medium text-blue-700">实时反馈</span>
        </div>
        <div class="space-y-1">
          <p 
            v-for="(message, index) in recentFeedback" 
            :key="index"
            class="text-sm text-blue-600"
          >
            {{ message }}
          </p>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { computed } from 'vue'
import { 
  TrendCharts, 
  Clock, 
  Timer, 
  Stopwatch, 
  Flag, 
  ChatDotRound 
} from '@element-plus/icons-vue'

// Props定义
const props = defineProps({
  currentAction: { 
    type: Object, 
    default: null 
  },
  progress: {
    type: Number,
    default: 0,
    validator: (value) => value >= 0 && value <= 100
  }
})

// 计算属性
const overallProgress = computed(() => {
  if (!props.currentAction) return props.progress
  
  const { current_sets, total_sets, current_reps, reps_per_set } = props.currentAction
  const totalReps = total_sets * reps_per_set
  const completedReps = (current_sets - 1) * reps_per_set + current_reps
  
  return Math.min(100, Math.round((completedReps / totalReps) * 100))
})

const currentSetProgress = computed(() => {
  if (!props.currentAction) return 0
  
  const { current_reps, reps_per_set } = props.currentAction
  return Math.min(100, Math.round((current_reps / reps_per_set) * 100))
})

const completedReps = computed(() => {
  if (!props.currentAction) return 0
  
  const { current_sets, current_reps, reps_per_set } = props.currentAction
  return (current_sets - 1) * reps_per_set + current_reps
})

const totalReps = computed(() => {
  if (!props.currentAction) return 0
  
  const { total_sets, reps_per_set } = props.currentAction
  return total_sets * reps_per_set
})

const hasScores = computed(() => {
  return props.currentAction?.current_scores?.length > 0
})

const averageScore = computed(() => {
  if (!hasScores.value) return 0
  
  const scores = props.currentAction.current_scores
  const sum = scores.reduce((acc, score) => acc + score, 0)
  return Math.round(sum / scores.length)
})



const hasRecentFeedback = computed(() => {
  return props.currentAction?.feedback_messages?.length > 0
})

const recentFeedback = computed(() => {
  if (!hasRecentFeedback.value) return []
  
  return props.currentAction.feedback_messages.slice(-3)
})

const trainingDuration = computed(() => {
  if (!props.currentAction?.start_time) return 0
  
  const endTime = props.currentAction.end_time || Date.now() / 1000
  return Math.round(endTime - props.currentAction.start_time)
})

// 方法
const getActionDisplayName = () => {
  if (!props.currentAction?.action_info?.action_type) return '未知动作'
  
  const nameMap = {
    'shoulder_touch': '肩部触摸',
    'arm_raise': '手臂举起',
    'finger_touch': '手指触摸',
    'palm_flip': '手掌翻转'
  }
  
  return nameMap[props.currentAction.action_info.action_type] || props.currentAction.action_info.action_type
}

const getStatusText = () => {
  if (!props.currentAction) return ''
  
  const statusMap = {
    'pending': '准备中',
    'active': '进行中',
    'completed': '已完成',
    'failed': '失败'
  }
  
  return statusMap[props.currentAction.action_status] || props.currentAction.action_status
}

const getStatusTagType = () => {
  if (!props.currentAction) return 'info'
  
  const typeMap = {
    'pending': 'warning',
    'active': 'primary',
    'completed': 'success',
    'failed': 'danger'
  }
  
  return typeMap[props.currentAction.action_status] || 'info'
}

const getActionStatusType = () => {
  return getStatusTagType()
}

const getActionStatusText = () => {
  return getStatusText()
}

const getProgressColor = () => {
  if (overallProgress.value >= 80) return '#10b981'
  if (overallProgress.value >= 50) return '#3b82f6'
  return '#f59e0b'
}

const getScoreColor = () => {
  if (averageScore.value >= 80) return 'text-green-600'
  if (averageScore.value >= 60) return 'text-blue-600'
  return 'text-orange-600'
}



const formatTime = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins}:${secs.toString().padStart(2, '0')}`
}
</script>

<style scoped>
.progress-display-card {
  height: 100%;
}

.progress-display-card :deep(.el-card__body) {
  padding: 1rem;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .grid-cols-2 {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}
</style>
