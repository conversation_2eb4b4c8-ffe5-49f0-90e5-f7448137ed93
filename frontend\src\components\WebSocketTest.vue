<template>
  <div class="websocket-test p-6 max-w-6xl mx-auto">
    <h1 class="text-3xl font-bold mb-6 text-center">WebSocket 连接测试</h1>
    
    <!-- 连接控制面板 -->
    <div class="connection-panel bg-white rounded-lg shadow-md p-6 mb-6">
      <h2 class="text-xl font-semibold mb-4">连接控制</h2>
      
      <div class="flex items-center gap-4 mb-4">
        <input 
          v-model="serverUrl" 
          type="text" 
          placeholder="服务器地址"
          class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <button 
          @click="connect" 
          :disabled="isConnected"
          class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-400"
        >
          连接
        </button>
        <button 
          @click="disconnect" 
          :disabled="!isConnected"
          class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 disabled:bg-gray-400"
        >
          断开
        </button>
      </div>
      
      <!-- 连接状态 -->
      <div class="status-display flex items-center gap-4">
        <div class="flex items-center gap-2">
          <div 
            :class="[
              'w-3 h-3 rounded-full',
              isConnected ? 'bg-green-500' : 'bg-red-500'
            ]"
          ></div>
          <span class="font-medium">
            状态: {{ connectionStatus }}
          </span>
        </div>
        
        <div v-if="socketId" class="text-sm text-gray-600">
          Socket ID: {{ socketId }}
        </div>
        
        <div v-if="lastError" class="text-sm text-red-600">
          错误: {{ lastError }}
        </div>
      </div>
    </div>

    <!-- 消息发送面板 -->
    <div class="message-panel bg-white rounded-lg shadow-md p-6 mb-6">
      <h2 class="text-xl font-semibold mb-4">消息发送</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <button 
          @click="sendSystemReset"
          :disabled="!isConnected"
          class="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 disabled:bg-gray-400"
        >
          发送系统重置
        </button>
        
        <button 
          @click="sendCustomMessage"
          :disabled="!isConnected"
          class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:bg-gray-400"
        >
          发送自定义消息
        </button>
      </div>
      
      <!-- 自定义消息输入 -->
      <div class="custom-message-input grid grid-cols-1 md:grid-cols-3 gap-2">
        <input 
          v-model="customEvent" 
          type="text" 
          placeholder="事件名称"
          class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <input 
          v-model="customData" 
          type="text" 
          placeholder="消息数据 (JSON)"
          class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <button 
          @click="sendCustom"
          :disabled="!isConnected"
          class="px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 disabled:bg-gray-400"
        >
          发送
        </button>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-panel bg-white rounded-lg shadow-md p-6 mb-6">
      <h2 class="text-xl font-semibold mb-4">统计信息</h2>
      
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div class="stat-item text-center">
          <div class="text-2xl font-bold text-blue-600">{{ stats.messagesReceived }}</div>
          <div class="text-sm text-gray-600">接收消息</div>
        </div>
        <div class="stat-item text-center">
          <div class="text-2xl font-bold text-green-600">{{ stats.messagesSent }}</div>
          <div class="text-sm text-gray-600">发送消息</div>
        </div>
        <div class="stat-item text-center">
          <div class="text-2xl font-bold text-purple-600">{{ stats.connectionCount }}</div>
          <div class="text-sm text-gray-600">连接次数</div>
        </div>
        <div class="stat-item text-center">
          <div class="text-sm font-medium text-gray-800">
            {{ stats.lastMessageTime ? formatTime(stats.lastMessageTime) : '无' }}
          </div>
          <div class="text-sm text-gray-600">最后消息</div>
        </div>
      </div>
    </div>

    <!-- 消息历史 -->
    <div class="message-history bg-white rounded-lg shadow-md p-6">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold">消息历史</h2>
        <button 
          @click="clearHistory"
          class="px-3 py-1 bg-gray-500 text-white rounded-md hover:bg-gray-600 text-sm"
        >
          清空历史
        </button>
      </div>
      
      <div class="message-list max-h-96 overflow-y-auto space-y-2">
        <div 
          v-for="(message, index) in messageHistory" 
          :key="index"
          :class="[
            'message-item p-3 rounded-md border-l-4',
            message.type === 'received' ? 'bg-blue-50 border-blue-500' :
            message.type === 'sent' ? 'bg-green-50 border-green-500' :
            message.type === 'error' ? 'bg-red-50 border-red-500' :
            'bg-gray-50 border-gray-500'
          ]"
        >
          <div class="flex justify-between items-start mb-1">
            <span class="font-medium text-sm">
              {{ message.type === 'received' ? '📨' : message.type === 'sent' ? '📤' : '⚠️' }}
              {{ message.event }}
            </span>
            <span class="text-xs text-gray-500">
              {{ formatTime(message.timestamp) }}
            </span>
          </div>
          <pre class="text-xs text-gray-700 whitespace-pre-wrap">{{ JSON.stringify(message.data, null, 2) }}</pre>
        </div>
        
        <div v-if="messageHistory.length === 0" class="text-center text-gray-500 py-8">
          暂无消息记录
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import websocketService from '../services/websocket.js'

// 响应式数据
const serverUrl = ref('http://localhost:5000')
const customEvent = ref('test_message')
const customData = ref('{"test": "data"}')

// 从服务中获取响应式数据
const isConnected = computed(() => websocketService.isConnected.value)
const connectionStatus = computed(() => websocketService.connectionStatus.value)
const lastError = computed(() => websocketService.lastError.value)
const stats = computed(() => websocketService.stats)
const messageHistory = computed(() => websocketService.messageHistory)
const socketId = computed(() => websocketService.socket?.id || null)

// 方法
const connect = () => {
  websocketService.connect(serverUrl.value)
}

const disconnect = () => {
  websocketService.disconnect()
}

const sendSystemReset = () => {
  websocketService.resetSystem()
}

const sendCustomMessage = () => {
  websocketService.emit('test_message', {
    message: 'Hello from frontend!',
    timestamp: new Date().toISOString(),
    source: 'websocket_test_component'
  })
}

const sendCustom = () => {
  try {
    const data = customData.value ? JSON.parse(customData.value) : {}
    websocketService.emit(customEvent.value, data)
  } catch (error) {
    alert('JSON 格式错误: ' + error.message)
  }
}

const clearHistory = () => {
  websocketService.clearHistory()
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString()
}

// 生命周期
onMounted(() => {
  console.log('WebSocket测试组件已挂载')
})

onUnmounted(() => {
  // 组件卸载时断开连接
  websocketService.disconnect()
})
</script>

<style scoped>
.websocket-test {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.message-item {
  transition: all 0.2s ease;
}

.message-item:hover {
  transform: translateX(2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

pre {
  font-family: 'Courier New', monospace;
  max-height: 100px;
  overflow-y: auto;
}
</style>
