<template>
  <div class="app-navigation">
    <!-- 顶部导航栏 -->
    <div class="nav-header bg-white shadow-sm border-b border-gray-200">
      <div class="flex items-center justify-between h-16 px-6">
        <!-- 左侧：Logo和标题 -->
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-primary-500 rounded-lg flex-center">
              <span class="text-white font-bold text-sm">康</span>
            </div>
            <h1 class="text-xl font-semibold text-gray-800">智能康复系统</h1>
          </div>
          
          <!-- 面包屑导航 -->
          <div v-if="showBreadcrumb" class="hidden md:block">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item 
                v-for="item in breadcrumbItems" 
                :key="item.title"
                :to="item.path"
              >
                {{ item.title }}
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>
        </div>
        
        <!-- 中间：系统状态指示器 -->
        <div class="flex items-center space-x-4">
          <!-- 连接状态 -->
          <div class="flex items-center space-x-2">
            <div 
              :class="[
                'w-2 h-2 rounded-full',
                connectionStatusClass
              ]"
            ></div>
            <span class="text-sm text-gray-600">{{ connectionStatusText }}</span>
          </div>
          
          <!-- 系统状态 -->
          <el-tag 
            :type="systemStateType" 
            size="small"
            class="hidden sm:inline-flex"
          >
            {{ systemStateText }}
          </el-tag>
        </div>
        
        <!-- 右侧：用户信息和操作 -->
        <div class="flex items-center space-x-4">
          <!-- 用户信息 -->
          <div v-if="isAuthenticated" class="flex items-center space-x-2">
            <el-avatar :size="32" class="bg-primary-500">
              <span class="text-white text-sm">{{ userInitial }}</span>
            </el-avatar>
            <div class="hidden sm:block">
              <div class="text-sm font-medium text-gray-800">{{ userDisplayName }}</div>
              <div class="text-xs text-gray-500">在线时长: {{ sessionDurationText }}</div>
            </div>
          </div>
          
          <!-- 操作按钮 -->
          <div class="flex items-center space-x-2">
            <!-- 系统健康度 -->
            <el-tooltip content="系统健康度" placement="bottom">
              <div class="flex items-center space-x-1 px-2 py-1 rounded bg-gray-100">
                <el-icon :size="14" :class="healthIconClass">
                  <component :is="healthIcon" />
                </el-icon>
                <span class="text-xs font-medium">{{ systemHealth }}%</span>
              </div>
            </el-tooltip>
            
            <!-- 设置按钮 -->
            <el-button 
              circle 
              size="small"
              @click="handleSettings"
              :disabled="!isAuthenticated"
            >
              <el-icon><Setting /></el-icon>
            </el-button>
            
            <!-- 帮助按钮 -->
            <el-button 
              circle 
              size="small"
              @click="handleHelp"
            >
              <el-icon><QuestionFilled /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 快速导航（可选） -->
    <div v-if="showQuickNav" class="quick-nav bg-gray-50 border-b border-gray-200">
      <div class="flex items-center justify-center space-x-6 py-2">
        <el-button 
          v-for="navItem in quickNavItems"
          :key="navItem.name"
          :type="navItem.active ? 'primary' : 'default'"
          :disabled="!navItem.enabled"
          size="small"
          @click="handleQuickNav(navItem)"
        >
          <el-icon class="mr-1">
            <component :is="navItem.icon" />
          </el-icon>
          {{ navItem.label }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { useBreadcrumb, useNavigation } from '@/utils/useRouter'
import { useMainStore } from '@/stores/main'
import { 
  Setting, 
  QuestionFilled, 
  CircleCheckFilled, 
  WarningFilled, 
  CircleCloseFilled,
  User,
  Monitor,
  Document
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  showBreadcrumb: {
    type: Boolean,
    default: true
  },
  showQuickNav: {
    type: Boolean,
    default: false
  }
})

// 组合式API
const { breadcrumbItems } = useBreadcrumb()
const { 
  currentRouteName,
  navigateToLogin,
  navigateToTraining,
  navigateToReport 
} = useNavigation()

// 使用主store
const mainStore = useMainStore()

// 系统状态
const isConnected = computed(() => mainStore.isConnected)
const connectionStatus = computed(() => mainStore.isConnected ? 'connected' : 'disconnected')
const currentState = computed(() => mainStore.currentState)

// 用户状态
const isAuthenticated = computed(() => mainStore.isUserLoggedIn)
const userDisplayName = computed(() => mainStore.userInfo?.name || mainStore.userInfo?.patient_id || '用户')
const sessionDuration = computed(() => 0) // 简化实现

// 计算属性
const connectionStatusClass = computed(() => {
  if (isConnected.value) return 'bg-green-500'
  return 'bg-red-500'
})

const connectionStatusText = computed(() => {
  if (isConnected.value) return '已连接'
  return '未连接'
})

const systemStateType = computed(() => {
  const stateTypeMap = {
    'IDLE': 'info',
    'WAITING': 'warning',
    'USER_LOGIN': 'primary',
    'ACTION_PREPARATION': 'success',
    'ACTION_TRAINING': 'success',
    'ACTION_REST': 'warning',
    'ACTION_COMPLETED': 'success'
  }
  
  return stateTypeMap[currentState.value] || 'info'
})

const systemStateText = computed(() => {
  const stateTextMap = {
    'IDLE': '空闲',
    'WAITING': '等待',
    'USER_LOGIN': '登录中',
    'ACTION_PREPARATION': '准备中',
    'ACTION_TRAINING': '训练中',
    'ACTION_REST': '休息中',
    'ACTION_COMPLETED': '已完成'
  }
  
  return stateTextMap[currentState.value] || '未知'
})

const userInitial = computed(() => {
  if (!userDisplayName.value) return '?'
  return userDisplayName.value.charAt(0).toUpperCase()
})

const sessionDurationText = computed(() => {
  if (!sessionDuration.value) return '0分钟'
  
  const minutes = Math.floor(sessionDuration.value / (1000 * 60))
  const hours = Math.floor(minutes / 60)
  
  if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`
  }
  return `${minutes}分钟`
})

const healthIcon = computed(() => {
  if (systemHealth.value >= 80) return CircleCheckFilled
  if (systemHealth.value >= 60) return WarningFilled
  return CircleCloseFilled
})

const healthIconClass = computed(() => {
  if (systemHealth.value >= 80) return 'text-green-500'
  if (systemHealth.value >= 60) return 'text-yellow-500'
  return 'text-red-500'
})

const quickNavItems = computed(() => [
  {
    name: 'login',
    label: '登录',
    icon: User,
    active: currentRouteName.value === 'Login',
    enabled: true
  },
  {
    name: 'training',
    label: '训练',
    icon: Monitor,
    active: currentRouteName.value === 'Training',
    enabled: isAuthenticated.value
  },
  {
    name: 'report',
    label: '报告',
    icon: Document,
    active: currentRouteName.value === 'Report',
    enabled: isAuthenticated.value
  }
])

// 方法
const handleSettings = () => {
  ElMessage.info('设置功能开发中...')
}

const handleHelp = () => {
  ElMessage.info('帮助功能开发中...')
}

const handleQuickNav = async (navItem) => {
  switch (navItem.name) {
    case 'login':
      await navigateToLogin()
      break
    case 'training':
      await navigateToTraining()
      break
    case 'report':
      await navigateToReport()
      break
  }
}
</script>

<style scoped>
.app-navigation {
  position: relative;
  z-index: 100;
}

.nav-header {
  backdrop-filter: blur(8px);
}

.quick-nav {
  backdrop-filter: blur(4px);
}

.el-breadcrumb {
  font-size: 14px;
}

.el-breadcrumb :deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
  color: #409eff;
  font-weight: 500;
}

.el-avatar {
  border: 2px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-header .px-6 {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .nav-header h1 {
    font-size: 1.125rem;
  }
}

@media (max-width: 640px) {
  .quick-nav .space-x-6 {
    gap: 0.5rem;
  }
  
  .quick-nav .el-button {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }
}
</style>
