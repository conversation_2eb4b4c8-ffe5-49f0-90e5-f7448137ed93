<template>
  <div class="space-y-4">
    <!-- 检测状态卡片 -->
    <div :class="[
      'p-4 rounded-xl border-2 transition-all duration-300',
      statusCardClass === 'idle' ? 'bg-gray-50 border-gray-200' : '',
      statusCardClass === 'detecting' ? 'bg-blue-50 border-blue-400 animate-pulse' : '',
      statusCardClass === 'success' ? 'bg-green-50 border-green-400' : '',
      statusCardClass === 'failed' ? 'bg-red-50 border-red-400' : ''
    ]">
      <div class="flex items-center gap-3 mb-4">
        <div class="flex-shrink-0">
          <el-icon :size="24" :class="statusIconClass">
            <component :is="statusIcon" />
          </el-icon>
        </div>
        <div class="flex-1">
          <h3 class="text-base font-semibold text-gray-700 mb-1">{{ statusTitle }}</h3>
          <p class="text-sm text-gray-600 leading-relaxed">{{ statusDescription }}</p>
        </div>
      </div>

      <!-- 检测进度 -->
      <div v-if="isDetecting" class="mt-4">
        <el-progress
          :percentage="detectionProgress"
          :show-text="false"
          :stroke-width="4"
          :color="progressColor"
        />
        <div class="flex justify-between mt-2 text-xs text-gray-500">
          <span>检测进度: {{ detectionProgress }}%</span>
          <span>{{ detectionTimeText }}</span>
        </div>
      </div>
    </div>

    <!-- 检测统计 -->
    <div class="bg-gray-50 rounded-xl p-4">
      <div class="grid grid-cols-3 gap-4 sm:gap-6">
        <div class="text-center sm:flex sm:justify-between sm:items-center sm:text-left">
          <div class="text-2xl sm:text-xl font-bold text-gray-700 mb-1 sm:mb-0">{{ detectionCount }}</div>
          <div class="text-xs text-gray-500 uppercase tracking-wide">检测次数</div>
        </div>
        <div class="text-center sm:flex sm:justify-between sm:items-center sm:text-left">
          <div class="text-2xl sm:text-xl font-bold text-gray-700 mb-1 sm:mb-0">{{ successRate }}%</div>
          <div class="text-xs text-gray-500 uppercase tracking-wide">成功率</div>
        </div>
        <div class="text-center sm:flex sm:justify-between sm:items-center sm:text-left">
          <div class="text-2xl sm:text-xl font-bold text-gray-700 mb-1 sm:mb-0">{{ avgConfidence }}%</div>
          <div class="text-xs text-gray-500 uppercase tracking-wide">平均置信度</div>
        </div>
      </div>
    </div>

    <!-- 最近检测结果 -->
    <div v-if="lastDetection" class="bg-white border border-gray-200 rounded-xl p-4">
      <div class="flex justify-between items-center mb-3">
        <span class="font-semibold text-gray-700">最近检测</span>
        <span class="text-xs text-gray-500">{{ lastDetectionTime }}</span>
      </div>

      <div :class="[
        'flex items-center gap-2 p-3 rounded-lg',
        lastDetectionClass === 'success' ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
      ]">
        <div class="flex-shrink-0">
          <el-icon :size="16">
            <component :is="lastDetectionIcon" />
          </el-icon>
        </div>
        <div class="flex-1">
          <div class="font-medium mb-1">{{ lastDetectionStatus }}</div>
          <div v-if="lastDetection.confidence" class="text-xs opacity-80">
            置信度: {{ (lastDetection.confidence * 100).toFixed(1) }}%
          </div>
          <div v-if="lastDetection.patientId" class="text-xs opacity-80">
            患者ID: {{ lastDetection.patientId }}
          </div>
        </div>
      </div>
    </div>

    <!-- 检测历史 -->
    <div v-if="showHistory && detectionHistory.length > 0" class="bg-white border border-gray-200 rounded-xl p-4">
      <div class="flex justify-between items-center mb-3">
        <span class="font-semibold text-gray-700">检测历史</span>
        <el-button size="small" text @click="clearHistory" class="text-gray-500 hover:text-red-500">
          <el-icon><Delete /></el-icon>
          清空
        </el-button>
      </div>

      <div class="space-y-2">
        <div
          v-for="(detection, index) in recentHistory"
          :key="index"
          :class="[
            'flex items-center justify-between p-2 rounded text-xs',
            detection.result ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
          ]"
        >
          <div class="flex-shrink-0">{{ formatTime(detection.timestamp) }}</div>
          <div class="flex items-center gap-1">
            <el-icon :size="12">
              <component :is="detection.result ? 'CircleCheckFilled' : 'CircleCloseFilled'" />
            </el-icon>
            <span>{{ detection.result ? '成功' : '失败' }}</span>
          </div>
          <div v-if="detection.confidence" class="font-medium">
            {{ (detection.confidence * 100).toFixed(0) }}%
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  User,
  Loading,
  CircleCheckFilled,
  CircleCloseFilled,
  Delete
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  detectionCount: {
    type: Number,
    default: 0
  },
  lastDetection: {
    type: Object,
    default: null
  },
  isDetecting: {
    type: Boolean,
    default: false
  },
  showHistory: {
    type: Boolean,
    default: true
  },
  maxHistoryItems: {
    type: Number,
    default: 10
  }
})

// Emits
const emit = defineEmits(['detection-result', 'clear-history'])

// 组件状态
const detectionProgress = ref(0)
const detectionStartTime = ref(null)
const detectionHistory = ref([])
const progressInterval = ref(null)

// 计算属性
const statusIcon = computed(() => {
  if (props.isDetecting) return Loading
  if (props.lastDetection?.result) return CircleCheckFilled
  if (props.lastDetection && !props.lastDetection.result) return CircleCloseFilled
  return User
})

const statusIconClass = computed(() => {
  if (props.isDetecting) return 'text-blue-500 animate-spin'
  if (props.lastDetection?.result) return 'text-green-500'
  if (props.lastDetection && !props.lastDetection.result) return 'text-red-500'
  return 'text-gray-500'
})

const statusCardClass = computed(() => {
  if (props.isDetecting) return 'detecting'
  if (props.lastDetection?.result) return 'success'
  if (props.lastDetection && !props.lastDetection.result) return 'failed'
  return 'idle'
})

const statusTitle = computed(() => {
  if (props.isDetecting) {
    if (detectionProgress.value < 30) {
      return '🔍 检测用户姿态'
    } else if (detectionProgress.value < 70) {
      return '🧠 分析用户特征'
    } else {
      return '✨ 验证用户身份'
    }
  }
  if (props.lastDetection?.result) return '✅ 检测成功'
  if (props.lastDetection && !props.lastDetection.result) return '❌ 检测失败'
  return '👤 等待检测'
})

const statusDescription = computed(() => {
  if (props.isDetecting) {
    if (detectionProgress.value < 30) {
      return '正在检测用户姿态，请保持在摄像头前...'
    } else if (detectionProgress.value < 70) {
      return '正在分析用户特征，请稍候...'
    } else {
      return '正在验证用户身份，即将完成...'
    }
  }
  if (props.lastDetection?.result) return '用户身份验证成功，准备进入训练系统'
  if (props.lastDetection && !props.lastDetection.result) return '未能识别用户身份，请重新尝试'
  return '请站在摄像头前开始身份检测'
})

const progressColor = computed(() => {
  if (detectionProgress.value < 30) return '#f56c6c'
  if (detectionProgress.value < 70) return '#e6a23c'
  return '#67c23a'
})

const detectionTimeText = computed(() => {
  if (!detectionStartTime.value) return ''
  
  const now = new Date()
  const elapsed = Math.floor((now.getTime() - detectionStartTime.value.getTime()) / 1000)
  return `已用时: ${elapsed}秒`
})

const successRate = computed(() => {
  if (detectionHistory.value.length === 0) return 0
  
  const successCount = detectionHistory.value.filter(d => d.result).length
  return Math.round((successCount / detectionHistory.value.length) * 100)
})

const avgConfidence = computed(() => {
  const validDetections = detectionHistory.value.filter(d => d.confidence > 0)
  if (validDetections.length === 0) return 0
  
  const totalConfidence = validDetections.reduce((sum, d) => sum + d.confidence, 0)
  return Math.round((totalConfidence / validDetections.length) * 100)
})

const lastDetectionTime = computed(() => {
  if (!props.lastDetection?.timestamp) return ''
  
  const time = new Date(props.lastDetection.timestamp)
  return time.toLocaleTimeString()
})

const lastDetectionClass = computed(() => {
  if (!props.lastDetection) return ''
  return props.lastDetection.result ? 'success' : 'failed'
})

const lastDetectionIcon = computed(() => {
  if (!props.lastDetection) return User
  return props.lastDetection.result ? CircleCheckFilled : CircleCloseFilled
})

const lastDetectionStatus = computed(() => {
  if (!props.lastDetection) return '无检测记录'
  return props.lastDetection.result ? '检测成功' : '检测失败'
})

const recentHistory = computed(() => {
  return detectionHistory.value.slice(-props.maxHistoryItems).reverse()
})

// 监听检测状态变化
watch(() => props.isDetecting, (detecting) => {
  if (detecting) {
    startDetectionProgress()
  } else {
    stopDetectionProgress()
  }
})

// 监听最新检测结果
watch(() => props.lastDetection, (detection) => {
  if (detection) {
    addToHistory(detection)
    emit('detection-result', detection)
  }
}, { deep: true })

// 生命周期
onMounted(() => {
  // 初始化检测历史
  loadDetectionHistory()
})

// 方法
const startDetectionProgress = () => {
  detectionProgress.value = 0
  detectionStartTime.value = new Date()
  
  progressInterval.value = setInterval(() => {
    if (detectionProgress.value < 95) {
      detectionProgress.value += Math.random() * 5
    }
  }, 200)
}

const stopDetectionProgress = () => {
  if (progressInterval.value) {
    clearInterval(progressInterval.value)
    progressInterval.value = null
  }
  
  detectionProgress.value = 100
  
  // 延迟重置进度
  setTimeout(() => {
    detectionProgress.value = 0
    detectionStartTime.value = null
  }, 1000)
}

const addToHistory = (detection) => {
  const historyItem = {
    timestamp: detection.timestamp || new Date(),
    result: detection.result || false,
    confidence: detection.confidence || 0,
    patientId: detection.patientId || null
  }
  
  detectionHistory.value.push(historyItem)
  
  // 限制历史记录数量
  if (detectionHistory.value.length > 50) {
    detectionHistory.value = detectionHistory.value.slice(-50)
  }
  
  // 保存到本地存储
  saveDetectionHistory()
}

const clearHistory = () => {
  detectionHistory.value = []
  saveDetectionHistory()
  emit('clear-history')
  ElMessage.success('检测历史已清空')
}

const loadDetectionHistory = () => {
  try {
    const saved = localStorage.getItem('detection_history')
    if (saved) {
      detectionHistory.value = JSON.parse(saved)
    }
  } catch (error) {
    console.warn('加载检测历史失败:', error)
  }
}

const saveDetectionHistory = () => {
  try {
    localStorage.setItem('detection_history', JSON.stringify(detectionHistory.value))
  } catch (error) {
    console.warn('保存检测历史失败:', error)
  }
}

const formatTime = (timestamp) => {
  const time = new Date(timestamp)
  return time.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit', 
    second: '2-digit' 
  })
}

// 暴露方法给父组件
defineExpose({
  clearHistory,
  getHistory: () => detectionHistory.value,
  getStats: () => ({
    total: detectionHistory.value.length,
    success: detectionHistory.value.filter(d => d.result).length,
    successRate: successRate.value,
    avgConfidence: avgConfidence.value
  })
})
</script>


