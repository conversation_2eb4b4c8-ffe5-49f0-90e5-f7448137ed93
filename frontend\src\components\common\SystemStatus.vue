<template>
  <div class="space-y-4">
    <!-- 系统健康度总览 -->
    <div class="flex flex-col md:flex-row items-center gap-4 p-4 bg-white border border-gray-200 rounded-xl">
      <div class="relative w-20 h-20 flex-shrink-0">
        <!-- 健康度圆环 -->
        <div :class="[
          'w-20 h-20 rounded-full flex flex-col items-center justify-center relative',
          healthLevelClass === 'excellent' ? 'bg-gradient-conic-from-green' : '',
          healthLevelClass === 'good' ? 'bg-gradient-conic-from-yellow' : '',
          healthLevelClass === 'fair' ? 'bg-gradient-conic-from-orange' : '',
          healthLevelClass === 'poor' ? 'bg-gradient-conic-from-red' : ''
        ]">
          <div class="absolute inset-2 bg-white rounded-full flex flex-col items-center justify-center">
            <div class="text-lg font-bold text-gray-700">{{ systemHealth }}%</div>
            <div class="text-xs text-gray-500">健康度</div>
          </div>
        </div>
      </div>
      <div class="flex-1 text-center md:text-left">
        <h4 class="text-base font-semibold text-gray-700 mb-2">{{ healthTitle }}</h4>
        <p class="text-sm text-gray-600 leading-relaxed">{{ healthDescription }}</p>
      </div>
    </div>
    
    <!-- 状态指标网格 -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
      <!-- 连接状态 -->
      <div :class="[
        'flex items-center gap-3 p-4 rounded-xl border transition-all duration-200 hover:shadow-md',
        connectionStatusClass === 'success' ? 'border-green-400 bg-green-50' : '',
        connectionStatusClass === 'warning' ? 'border-yellow-400 bg-yellow-50' : '',
        connectionStatusClass === 'error' ? 'border-red-400 bg-red-50' : '',
        connectionStatusClass === 'normal' ? 'border-gray-200 bg-white' : ''
      ]">
        <div :class="[
          'flex-shrink-0',
          connectionStatusClass === 'success' ? 'text-green-500' : '',
          connectionStatusClass === 'warning' ? 'text-yellow-500' : '',
          connectionStatusClass === 'error' ? 'text-red-500' : '',
          connectionStatusClass === 'normal' ? 'text-gray-500' : ''
        ]">
          <el-icon :size="20">
            <component :is="connectionIcon" />
          </el-icon>
        </div>
        <div class="flex-1">
          <div class="text-xs text-gray-500 mb-1">连接状态</div>
          <div class="text-sm font-semibold text-gray-700">{{ connectionStatusText }}</div>
        </div>
        <div class="flex-shrink-0">
          <div :class="[
            'w-2 h-2 rounded-full animate-pulse',
            connectionIndicatorClass === 'success' ? 'bg-green-400' : '',
            connectionIndicatorClass === 'warning' ? 'bg-yellow-400' : '',
            connectionIndicatorClass === 'error' ? 'bg-red-400' : '',
            connectionIndicatorClass === 'normal' ? 'bg-gray-400' : ''
          ]"></div>
        </div>
      </div>

      <!-- 错误计数 -->
      <div :class="[
        'flex items-center gap-3 p-4 rounded-xl border transition-all duration-200 hover:shadow-md',
        errorStatusClass === 'success' ? 'border-green-400 bg-green-50' : '',
        errorStatusClass === 'warning' ? 'border-yellow-400 bg-yellow-50' : '',
        errorStatusClass === 'error' ? 'border-red-400 bg-red-50' : ''
      ]">
        <div :class="[
          'flex-shrink-0',
          errorStatusClass === 'success' ? 'text-green-500' : '',
          errorStatusClass === 'warning' ? 'text-yellow-500' : '',
          errorStatusClass === 'error' ? 'text-red-500' : ''
        ]">
          <el-icon :size="20">
            <WarningFilled />
          </el-icon>
        </div>
        <div class="flex-1">
          <div class="text-xs text-gray-500 mb-1">系统错误</div>
          <div class="text-sm font-semibold text-gray-700">{{ errorCount }} 个</div>
        </div>
        <div class="flex-shrink-0">
          <div :class="[
            'w-2 h-2 rounded-full animate-pulse',
            errorIndicatorClass === 'success' ? 'bg-green-400' : '',
            errorIndicatorClass === 'warning' ? 'bg-yellow-400' : '',
            errorIndicatorClass === 'error' ? 'bg-red-400' : ''
          ]"></div>
        </div>
      </div>

      <!-- 运行时间 -->
      <div class="flex items-center gap-3 p-4 rounded-xl border border-gray-200 bg-white transition-all duration-200 hover:shadow-md">
        <div class="flex-shrink-0 text-gray-500">
          <el-icon :size="20">
            <Timer />
          </el-icon>
        </div>
        <div class="flex-1">
          <div class="text-xs text-gray-500 mb-1">运行时间</div>
          <div class="text-sm font-semibold text-gray-700">{{ uptime }}</div>
        </div>
        <div class="flex-shrink-0">
          <div class="w-2 h-2 rounded-full bg-green-400 animate-pulse"></div>
        </div>
      </div>

      <!-- 内存使用 -->
      <div :class="[
        'flex items-center gap-3 p-4 rounded-xl border transition-all duration-200 hover:shadow-md',
        memoryStatusClass === 'success' ? 'border-green-400 bg-green-50' : '',
        memoryStatusClass === 'warning' ? 'border-yellow-400 bg-yellow-50' : '',
        memoryStatusClass === 'error' ? 'border-red-400 bg-red-50' : ''
      ]">
        <div :class="[
          'flex-shrink-0',
          memoryStatusClass === 'success' ? 'text-green-500' : '',
          memoryStatusClass === 'warning' ? 'text-yellow-500' : '',
          memoryStatusClass === 'error' ? 'text-red-500' : ''
        ]">
          <el-icon :size="20">
            <Monitor />
          </el-icon>
        </div>
        <div class="flex-1">
          <div class="text-xs text-gray-500 mb-1">内存使用</div>
          <div class="text-sm font-semibold text-gray-700">{{ memoryUsage }}MB</div>
        </div>
        <div class="flex-shrink-0">
          <div :class="[
            'w-2 h-2 rounded-full animate-pulse',
            memoryIndicatorClass === 'success' ? 'bg-green-400' : '',
            memoryIndicatorClass === 'warning' ? 'bg-yellow-400' : '',
            memoryIndicatorClass === 'error' ? 'bg-red-400' : ''
          ]"></div>
        </div>
      </div>
    </div>

    <!-- 详细状态信息 -->
    <div v-if="showDetails" class="bg-gray-50 border border-gray-200 rounded-xl p-4">
      <div class="flex justify-between items-center mb-3">
        <span class="font-semibold text-gray-700">详细信息</span>
        <el-button size="small" text @click="refreshStatus" class="text-gray-500 hover:text-blue-500">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>

      <div class="space-y-2">
        <div class="flex justify-between items-center text-sm">
          <span class="text-gray-500">WebSocket状态:</span>
          <span :class="[
            'font-medium',
            connectionStatusClass === 'success' ? 'text-green-600' : '',
            connectionStatusClass === 'warning' ? 'text-yellow-600' : '',
            connectionStatusClass === 'error' ? 'text-red-600' : '',
            connectionStatusClass === 'normal' ? 'text-gray-600' : ''
          ]">
            {{ connectionStatus }}
          </span>
        </div>
        <div class="flex justify-between items-center text-sm">
          <span class="text-gray-500">最后更新:</span>
          <span class="font-medium text-gray-700">{{ lastUpdateTime }}</span>
        </div>
        <div class="flex justify-between items-center text-sm">
          <span class="text-gray-500">系统版本:</span>
          <span class="font-medium text-gray-700">v1.0.0</span>
        </div>
        <div class="flex justify-between items-center text-sm">
          <span class="text-gray-500">浏览器:</span>
          <span class="font-medium text-gray-700">{{ browserInfo }}</span>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex gap-2 justify-center">
      <el-button
        size="small"
        @click="toggleDetails"
        class="rounded-lg"
      >
        <el-icon>
          <component :is="showDetails ? 'ArrowUp' : 'ArrowDown'" />
        </el-icon>
        {{ showDetails ? '收起详情' : '展开详情' }}
      </el-button>

      <el-button
        size="small"
        type="primary"
        @click="runDiagnostics"
        :loading="isDiagnosticRunning"
        class="rounded-lg"
      >
        <el-icon><Tools /></el-icon>
        系统诊断
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  SuccessFilled as Wifi,
  CircleCloseFilled as WifiOff ,
  WarningFilled,
  Timer,
  Monitor,
  Refresh,
  ArrowUp,
  ArrowDown,
  Tools
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  connectionStatus: {
    type: String,
    default: 'disconnected'
  },
  systemHealth: {
    type: Number,
    default: 100
  },
  errorCount: {
    type: Number,
    default: 0
  }
})

// Emits
const emit = defineEmits(['refresh-status', 'run-diagnostics'])

// 组件状态
const showDetails = ref(false)
const isDiagnosticRunning = ref(false)
const startTime = ref(new Date())
const currentTime = ref(new Date())
const memoryUsage = ref(0)
const lastUpdateTime = ref(new Date())

// 定时器
let timeUpdateInterval = null

// 计算属性
const healthLevelClass = computed(() => {
  const health = props.systemHealth
  if (health >= 90) return 'excellent'
  if (health >= 75) return 'good'
  if (health >= 50) return 'fair'
  return 'poor'
})

const healthTitle = computed(() => {
  const health = props.systemHealth
  if (health >= 90) return '系统运行良好'
  if (health >= 75) return '系统运行正常'
  if (health >= 50) return '系统需要关注'
  return '系统存在问题'
})

const healthDescription = computed(() => {
  const health = props.systemHealth
  if (health >= 90) return '所有系统组件运行正常，性能优秀'
  if (health >= 75) return '系统运行稳定，偶有小问题'
  if (health >= 50) return '系统运行缓慢，建议检查'
  return '系统存在严重问题，需要立即处理'
})

const connectionIcon = computed(() => {
  return props.connectionStatus === 'connected' ? Wifi : WifiOff
})

const connectionStatusText = computed(() => {
  const statusMap = {
    'connected': '已连接',
    'connecting': '连接中',
    'disconnected': '未连接',
    'error': '连接错误'
  }
  return statusMap[props.connectionStatus] || '未知'
})

const connectionStatusClass = computed(() => {
  const classMap = {
    'connected': 'success',
    'connecting': 'warning',
    'disconnected': 'error',
    'error': 'error'
  }
  return classMap[props.connectionStatus] || 'normal'
})

const connectionIndicatorClass = computed(() => {
  const classMap = {
    'connected': 'success',
    'connecting': 'warning',
    'disconnected': 'error',
    'error': 'error'
  }
  return classMap[props.connectionStatus] || 'normal'
})

const errorStatusClass = computed(() => {
  if (props.errorCount === 0) return 'success'
  if (props.errorCount <= 3) return 'warning'
  return 'error'
})

const errorIndicatorClass = computed(() => {
  if (props.errorCount === 0) return 'success'
  if (props.errorCount <= 3) return 'warning'
  return 'error'
})

const memoryStatusClass = computed(() => {
  if (memoryUsage.value < 100) return 'success'
  if (memoryUsage.value < 200) return 'warning'
  return 'error'
})

const memoryIndicatorClass = computed(() => {
  if (memoryUsage.value < 100) return 'success'
  if (memoryUsage.value < 200) return 'warning'
  return 'error'
})

const uptime = computed(() => {
  const diff = currentTime.value.getTime() - startTime.value.getTime()
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  const seconds = Math.floor((diff % (1000 * 60)) / 1000)
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  }
  return `${minutes}:${seconds.toString().padStart(2, '0')}`
})

const browserInfo = computed(() => {
  const ua = navigator.userAgent
  if (ua.includes('Chrome')) return 'Chrome'
  if (ua.includes('Firefox')) return 'Firefox'
  if (ua.includes('Safari')) return 'Safari'
  if (ua.includes('Edge')) return 'Edge'
  return 'Unknown'
})

// 生命周期
onMounted(() => {
  startTimeUpdate()
  updateMemoryUsage()
})

onUnmounted(() => {
  stopTimeUpdate()
})

// 方法
const startTimeUpdate = () => {
  timeUpdateInterval = setInterval(() => {
    currentTime.value = new Date()
    lastUpdateTime.value = new Date()
    
    // 定期更新内存使用情况
    if (currentTime.value.getSeconds() % 10 === 0) {
      updateMemoryUsage()
    }
  }, 1000)
}

const stopTimeUpdate = () => {
  if (timeUpdateInterval) {
    clearInterval(timeUpdateInterval)
    timeUpdateInterval = null
  }
}

const updateMemoryUsage = () => {
  if (performance.memory) {
    memoryUsage.value = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024)
  } else {
    // 模拟内存使用情况
    memoryUsage.value = Math.round(50 + Math.random() * 50)
  }
}

const toggleDetails = () => {
  showDetails.value = !showDetails.value
}

const refreshStatus = () => {
  lastUpdateTime.value = new Date()
  updateMemoryUsage()
  emit('refresh-status')
  ElMessage.success('状态已刷新')
}

const runDiagnostics = async () => {
  isDiagnosticRunning.value = true
  
  try {
    // 模拟诊断过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    emit('run-diagnostics')
    ElMessage.success('系统诊断完成，未发现问题')
    
  } catch (error) {
    console.error('系统诊断失败:', error)
    ElMessage.error('系统诊断失败')
  } finally {
    isDiagnosticRunning.value = false
  }
}

// 暴露方法给父组件
defineExpose({
  refreshStatus,
  runDiagnostics,
  getSystemInfo: () => ({
    uptime: uptime.value,
    memoryUsage: memoryUsage.value,
    browserInfo: browserInfo.value,
    lastUpdate: lastUpdateTime.value
  })
})
</script>


