<template>
  <div class="relative rounded-lg overflow-hidden bg-black" :style="containerStyle">
    <!-- 视频显示区域 -->
    <div class="w-full h-full relative" ref="videoDisplayRef">
      <!-- 模拟视频流 -->
      <div :class="[
        'w-full h-full flex items-center justify-center text-white transition-all duration-300',
        isConnected ? 'bg-gradient-to-br from-slate-800 to-slate-900' : '',
        isConnecting ? 'bg-gradient-to-br from-gray-800 to-gray-900' : '',
        hasError ? 'bg-gradient-to-br from-red-900 to-red-800' : '',
        !isConnected && !isConnecting && !hasError ? 'bg-gradient-to-br from-gray-700 to-gray-800' : ''
      ]">
        <div v-if="isConnecting" class="text-center">
          <el-icon class="text-blue-400 animate-spin mb-4" :size="32">
            <Loading />
          </el-icon>
          <p class="text-gray-300 text-sm">正在连接摄像头...</p>
        </div>

        <div v-else-if="hasError" class="text-center">
          <el-icon class="text-gray-400 mb-4" :size="32">
            <VideoCamera />
          </el-icon>
          <p class="text-gray-300 text-sm mb-3">{{ errorMessage }}</p>
          <el-button size="small" @click="reconnect" class="rounded-lg">重新连接</el-button>
        </div>

        <div v-else-if="isConnected" class="w-full h-full relative">
          <!-- 真实视频流 -->
          <img
            ref="videoStreamRef"
            :src="videoStreamUrl"
            class="w-full h-full object-cover"
            @error="handleVideoError"
            @loadstart="handleVideoLoadStart"
            alt="实时视频流"
          />

          <!-- 检测信息覆盖层 -->
          <div v-if="showDetectionInfo" class="absolute top-4 left-4 right-8">
            <div class="bg-black/70 backdrop-blur-sm rounded-lg px-4 py-2 flex justify-between items-center">
              <span :class="[
                'text-xs font-medium',
                userDetected ? 'text-green-400' : 'text-yellow-400'
              ]">
                {{ detectionStatusText }}
              </span>
                <span v-if="confidence > 0" class="text-xs text-gray-300 mr-12">
                置信度: {{ (confidence * 100).toFixed(1) }}%
                </span>
            </div>
          </div>

          <!-- 用户检测指示器 -->
          <div v-if="userDetected" class="absolute bottom-4 right-4">
            <div class="bg-green-500/80 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center gap-2">
              <div class="w-2 h-2 bg-white rounded-full animate-pulse"></div>
              用户已检测
            </div>
          </div>
        </div>

        <div v-else class="text-center">
          <el-icon class="text-gray-400 mb-4" :size="32">
            <VideoPause />
          </el-icon>
          <p class="text-gray-300 text-sm">视频流未连接</p>
        </div>
      </div>
    </div>

    <!-- 控制面板 -->
    <div v-if="showControls" class="absolute bottom-4 left-4 right-4">
      <div class="bg-black/80 backdrop-blur-lg rounded-lg p-3 flex flex-col sm:flex-row justify-between items-center gap-3">
        <el-button-group size="small">
          <el-button
            :type="isConnected ? 'danger' : 'primary'"
            @click="toggleConnection"
            :loading="isConnecting"
            class="rounded-l-lg"
          >
            <el-icon>
              <component :is="isConnected ? 'VideoPause' : 'VideoPlay'" />
            </el-icon>
            {{ isConnected ? '断开' : '连接' }}
          </el-button>

          <el-button @click="refreshStream" class="rounded-r-lg">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </el-button-group>

        <!-- 视频信息 -->
        <div class="flex gap-4 text-xs text-gray-300">
          <span class="flex items-center gap-1">
            <el-icon><Monitor /></el-icon>
            {{ resolution }}
          </span>
          <span class="flex items-center gap-1">
            <el-icon><Timer /></el-icon>
            {{ connectionTime }}
          </span>
        </div>
      </div>
    </div>

    <!-- 状态指示器 -->
    <div class="absolute top-4 right-4 flex items-center gap-2 bg-black/70 backdrop-blur-sm rounded-lg px-3 py-2">
      <div :class="[
        'w-2 h-2 rounded-full',
        connectionStatusClass === 'status-success' ? 'bg-green-400 animate-pulse' : '',
        connectionStatusClass === 'status-warning' ? 'bg-yellow-400 animate-pulse' : '',
        connectionStatusClass === 'status-error' ? 'bg-red-400 animate-pulse' : '',
        connectionStatusClass === 'status-inactive' ? 'bg-gray-400' : ''
      ]"></div>
      <span class="text-xs text-gray-300 font-medium">{{ connectionStatusText }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  Loading,
  VideoCamera,
  VideoPause,
  VideoPlay,
  Refresh,
  Monitor,
  Timer
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  width: {
    type: [String, Number],
    default: '100%'
  },
  height: {
    type: [String, Number],
    default: '240px'
  },
  showControls: {
    type: Boolean,
    default: true
  },
  showDetectionInfo: {
    type: Boolean,
    default: true
  },
  autoConnect: {
    type: Boolean,
    default: true
  },
  backendUrl: {
    type: String,
    default: 'http://localhost:5000'
  }
})

// Emits
const emit = defineEmits(['stream-ready', 'stream-error', 'user-detected', 'connection-change'])

// 模板引用
const videoDisplayRef = ref(null)
const videoStreamRef = ref(null)

// 组件状态
const isConnected = ref(false)
const isConnecting = ref(false)
const hasError = ref(false)
const errorMessage = ref('')
const connectionStartTime = ref(null)
const userDetected = ref(false)
const confidence = ref(0)

// 视频流相关
const videoStreamUrl = ref('')
const baseUrl = ref(props.backendUrl)
const resolution = ref('1920x1080')

// 计算属性
const containerStyle = computed(() => ({
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  height: typeof props.height === 'number' ? `${props.height}px` : props.height
}))

const connectionStatusClass = computed(() => {
  if (hasError.value) return 'status-error'
  if (isConnecting.value) return 'status-warning'
  if (isConnected.value) return 'status-success'
  return 'status-inactive'
})

const connectionStatusText = computed(() => {
  if (hasError.value) return '连接错误'
  if (isConnecting.value) return '连接中'
  if (isConnected.value) return '已连接'
  return '未连接'
})

const detectionStatusText = computed(() => {
  if (userDetected.value) return '检测到用户'
  return '正在检测...'
})

const connectionTime = computed(() => {
  if (!connectionStartTime.value) return '00:00'
  
  const now = new Date()
  const diff = now.getTime() - connectionStartTime.value.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  const seconds = Math.floor((diff % (1000 * 60)) / 1000)
  
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
})

// 生命周期
onMounted(() => {
  if (props.autoConnect) {
    connect()
  }
  
  // 模拟用户检测
  startUserDetectionSimulation()
})

onUnmounted(() => {
  disconnect()
  stopUserDetectionSimulation()
})

// 方法
const connect = async () => {
  if (isConnecting.value || isConnected.value) return

  try {
    isConnecting.value = true
    hasError.value = false
    errorMessage.value = ''

    // 首先测试后端接口是否可访问
    const testUrl = `${baseUrl.value}/video_feed`
    // 设置视频流URL
    videoStreamUrl.value = `${testUrl}?t=${Date.now()}`

    // 对于MJPEG流，我们直接认为连接成功
    // 因为MJPEG流不会触发标准的load事件
    setTimeout(() => {
      if (videoStreamUrl.value) {
        isConnected.value = true
        connectionStartTime.value = new Date()
        emit('stream-ready')
        emit('connection-change', { connected: true })
        console.log('视频流连接成功')
      }
    }, 100) // 给1秒时间让图片开始加载

  } catch (error) {
    hasError.value = true
    errorMessage.value = error.message || '连接失败'
    videoStreamUrl.value = ''
    emit('stream-error', error)
    console.error('视频流连接失败:', error)
  } finally {
    isConnecting.value = false
  }
}

const disconnect = () => {
  isConnected.value = false
  isConnecting.value = false
  connectionStartTime.value = null
  userDetected.value = false
  confidence.value = 0
  videoStreamUrl.value = ''

  emit('connection-change', { connected: false })
  console.log('视频流已断开')
}

const handleVideoLoadStart = () => {
  console.log('视频流开始加载')
  // 视频开始加载时清除错误状态
  hasError.value = false
  errorMessage.value = ''
}
const handleVideoError = (error) => {
  console.error('视频流错误事件:', error)
  // 只有在当前连接状态下才处理错误
  if (isConnected.value || isConnecting.value) {
    hasError.value = true
    errorMessage.value = '视频流加载失败'
    isConnected.value = false
    isConnecting.value = false
    videoStreamUrl.value = ''
    emit('stream-error', error)
  }
}

const toggleConnection = () => {
  if (isConnected.value) {
    disconnect()
  } else {
    connect()
  }
}

const reconnect = async () => {
  disconnect()
  await new Promise(resolve => setTimeout(resolve, 500))
  connect()
}

const refreshStream = () => {
  if (isConnected.value) {
    // 刷新视频流URL以强制重新加载
    videoStreamUrl.value = ''
    setTimeout(() => {
      videoStreamUrl.value = `${baseUrl.value}/video_feed?t=${Date.now()}`
    }, 100)
  } else {
    connect()
  }
}

// 模拟用户检测
let detectionInterval = null

const startUserDetectionSimulation = () => {
  detectionInterval = setInterval(() => {
    if (isConnected.value) {
      // 模拟随机检测结果
      const detected = Math.random() > 0.7
      const newConfidence = detected ? 0.8 + Math.random() * 0.2 : Math.random() * 0.3
      
      userDetected.value = detected
      confidence.value = newConfidence
      
      if (detected) {
        emit('user-detected', {
          detected: true,
          confidence: newConfidence,
          timestamp: new Date()
        })
      }
    }
  }, 3000)
}

const stopUserDetectionSimulation = () => {
  if (detectionInterval) {
    clearInterval(detectionInterval)
    detectionInterval = null
  }
}

// 暴露方法给父组件
defineExpose({
  connect,
  disconnect,
  reconnect,
  isConnected: () => isConnected.value,
  getStatus: () => ({
    connected: isConnected.value,
    connecting: isConnecting.value,
    error: hasError.value,
    userDetected: userDetected.value,
    confidence: confidence.value
  })
})
</script>


