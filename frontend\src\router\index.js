import { createRouter, createWebHistory } from 'vue-router'
import { useMainStore } from '@/stores/main'
import { useUserStore } from '@/stores/user'
import { useSystemStore } from '@/stores/system'

const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/LoginView.vue'),
    meta: {
      title: '用户登录',
      requiresAuth: false,
      allowedStates: ['IDLE', 'WAITING', 'USER_LOGIN']
    }
  },
  {
    path: '/training',
    name: 'Training',
    component: () => import('@/views/TrainingView.vue'),
    meta: {
      title: '康复训练',
      requiresAuth: true,
      allowedStates: ['ACTION_PREPARATION', 'ACTION_TRAINING', 'ACTION_REST']
    }
  },
  {
    path: '/report',
    name: 'Report',
    component: () => import('@/views/ReportView.vue'),
    meta: {
      title: '训练报告',
      requiresAuth: true,
      allowedStates: ['ACTION_COMPLETED', 'TRAINING_SESSION_ENDED']
    }
  },
  {
    path: '/error',
    name: 'Error',
    component: () => import('@/views/ErrorView.vue'),
    meta: {
      title: '系统错误',
      requiresAuth: false
    }
  },
  {
    path: '/test',
    name: 'Test',
    component: () => import('@/views/TestView.vue'),
    meta: {
      title: 'TailwindCSS测试',
      requiresAuth: false
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    redirect: '/login'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 页面切换时滚动到顶部
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局前置守卫 (更新为使用主store)
router.beforeEach(async (to, from, next) => {
  console.log(`路由导航: ${from.name || 'unknown'} → ${to.name}`)

  try {
    // 设置页面标题
    if (to.meta.title) {
      document.title = `${to.meta.title} - 智能康复系统`
    }

    // 获取store实例 (优先使用主store)
    const mainStore = useMainStore()
    const userStore = useUserStore()
    const systemStore = useSystemStore()

    // 检查系统连接状态 (从主store获取)
    if (!mainStore.isConnected && to.name !== 'Error') {
      console.log('系统未连接，尝试自动连接...')

      // 尝试自动连接WebSocket
      try {
        const websocketService = await import('@/services/websocket')
        await websocketService.default.connect()
        console.log('WebSocket自动连接成功')
      } catch (error) {
        console.error('WebSocket自动连接失败:', error)
        // 只有在非Login页面时才重定向到错误页面
        if (to.name !== 'Login') {
          console.warn('系统连接失败，重定向到错误页面')
          next({ name: 'Error', query: { error: 'connection_failed' } })
          return
        } else {
          // Login页面允许在未连接状态下访问，但会显示连接状态
          console.log('允许访问登录页面，连接将在页面中重试')
        }
      }
    }

    // 检查认证要求 (从主store获取)
    if (to.meta.requiresAuth && !mainStore.isUserLoggedIn) {
      console.warn('需要认证，重定向到登录页面')
      next({ name: 'Login', query: { redirect: to.fullPath } })
      return
    }

    // 检查系统状态要求 (从主store获取)
    if (to.meta.allowedStates && to.meta.allowedStates.length > 0) {
      const currentState = mainStore.currentState
      if (!to.meta.allowedStates.includes(currentState)) {
        console.warn(`当前系统状态 ${currentState} 不允许访问 ${to.name}`)

        // 根据当前状态重定向到合适的页面
        const redirectRoute = getRedirectRouteByState(currentState)
        if (redirectRoute && redirectRoute !== to.name) {
          console.log(`重定向到: ${redirectRoute}`)
          next({ name: redirectRoute })
          return
        }
      }
    }

    // 特殊路由逻辑
    if (to.name === 'Login' && mainStore.isUserLoggedIn) {
      // 已登录用户访问登录页，重定向到训练页
      console.log('用户已登录，重定向到训练页面')
      next({ name: 'Training' })
      return
    }

    // 训练页面访问检查
    if (to.name === 'Training') {
      // 检查是否有用户信息和训练任务
      if (!mainStore.userInfo || !mainStore.actionList || mainStore.actionList.length === 0) {
        console.warn('缺少训练数据，重定向到登录页面')
        next({ name: 'Login' })
        return
      }
    }

    // 报告页面访问检查
    if (to.name === 'Report') {
      // 检查是否有训练会话数据
      if (!mainStore.trainingSession) {
        console.warn('缺少训练会话数据，重定向到登录页面')
        next({ name: 'Login' })
        return
      }
    }

    // 记录路由访问
    logRouteAccess(to, from, mainStore)

    next()

  } catch (error) {
    console.error('路由守卫执行失败:', error)
    // 使用主store记录错误
    console.error('路由导航失败:', error.message)
    next({ name: 'Error', query: { error: 'navigation_failed' } })
  }
})

// 全局后置钩子 (更新为使用主store)
router.afterEach((to, from, failure) => {
  if (failure) {
    console.error('路由导航失败:', failure)
    // 记录导航失败，但不依赖简化后的store方法
    console.error('页面导航失败:', failure.message)
  } else {
    console.log(`路由导航完成: ${to.name}`)
  }
})

/**
 * 根据系统状态获取重定向路由 (更新状态映射)
 */
function getRedirectRouteByState(systemState) {
  const stateRouteMap = {
    'IDLE': 'Login',
    'WAITING': 'Login',
    'USER_LOGIN': 'Login',
    'ACTION_PREPARATION': 'Training',
    'ACTION_TRAINING': 'Training',
    'ACTION_REST': 'Training',
    'ACTION_COMPLETED': 'Report',
    'TRAINING_SESSION_ENDED': 'Report'
  }

  const route = stateRouteMap[systemState] || 'Login'
  console.log(`状态 ${systemState} 映射到路由: ${route}`)
  return route
}

/**
 * 记录路由访问日志 (更新为使用主store)
 */
function logRouteAccess(to, from, mainStore) {
  const accessLog = {
    timestamp: new Date().toISOString(),
    from: from.name || 'unknown',
    to: to.name,
    user: mainStore.userInfo?.name || mainStore.userInfo?.patient_id || '未知用户',
    systemState: mainStore.currentState,
    authenticated: mainStore.isUserLoggedIn,
    sessionId: mainStore.sessionId
  }

  console.log('路由访问记录:', accessLog)

  // 可以在这里添加路由访问统计到主store的状态历史
  if (mainStore.stateHistory) {
    mainStore.stateHistory.unshift({
      type: 'route_navigation',
      from: from.name,
      to: to.name,
      timestamp: Date.now()
    })
  }
}

/**
 * 编程式导航辅助函数
 */
export const navigationHelpers = {
  /**
   * 安全导航到指定路由 (更新错误处理)
   */
  async safePush(router, route) {
    try {
      await router.push(route)
    } catch (error) {
      console.error('导航失败:', error)
      // 简化错误处理，不依赖已简化的store方法
      console.error('页面跳转失败:', error.message)
    }
  },

  /**
   * 根据系统状态导航
   */
  navigateBySystemState(router, systemState) {
    const targetRoute = getRedirectRouteByState(systemState)
    if (targetRoute) {
      this.safePush(router, { name: targetRoute })
    }
  },

  /**
   * 带参数的安全导航 (更新错误处理)
   */
  async navigateWithParams(router, routeName, params = {}, query = {}) {
    try {
      await router.push({
        name: routeName,
        params,
        query
      })
    } catch (error) {
      console.error('参数导航失败:', error)
      // 简化错误处理，不依赖已简化的store方法
      console.error('页面跳转失败:', error.message)
    }
  }
}

export default router
