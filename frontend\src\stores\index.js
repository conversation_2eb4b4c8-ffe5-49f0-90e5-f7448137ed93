/**
 * Pinia Store 统一导出和初始化 (简化版)
 * 提供所有store的统一访问入口和初始化管理
 *
 * 注意：大部分功能已迁移到 stores/main.js
 */

import { useSystemStore } from './system'
import { useUserStore } from './user'
import { useMainStore } from './main'

/**
 * 导出所有store
 */
export {
  useSystemStore,
  useUserStore,
  useMainStore
}

/**
 * 初始化所有store (简化版)
 * 在应用启动时调用，主要初始化store实例
 */
export async function initializeStores() {
  console.log('开始初始化所有Pinia Store...')

  try {
    // 获取store实例
    const mainStore = useMainStore()
    const systemStore = useSystemStore()
    const userStore = useUserStore()

    // 加载用户偏好设置
    userStore.loadUserPreferences()

    console.log('所有Pinia Store初始化完成')
    console.log('注意: WebSocket连接将在需要时自动建立')

    return {
      mainStore,
      systemStore,
      userStore
    }

  } catch (error) {
    console.error('Store初始化失败:', error)
    throw error
  }
}

// 注意：Store协调逻辑已迁移到主store的WebSocket消息处理中

/**
 * 重置所有store状态
 * 用于系统重启或用户切换时清理状态
 */
export function resetAllStores() {
  console.log('重置所有Store状态...')

  // 重置主store
  const mainStore = useMainStore()
  mainStore.resetData()

  console.log('所有Store状态已重置')
}

/**
 * 获取应用整体状态摘要
 * 用于调试和状态监控
 */
export function getAppStateSummary() {
  const mainStore = useMainStore()

  return {
    timestamp: new Date().toISOString(),
    currentState: mainStore.currentState,
    isConnected: mainStore.isConnected,
    isUserLoggedIn: mainStore.isUserLoggedIn,
    sessionId: mainStore.sessionId
  }
}

/**
 * Store状态持久化
 * 保存重要状态到本地存储
 */
export function persistStoreState() {
  try {
    const userStore = useUserStore()
    // 保存用户偏好设置
    userStore.saveUserPreferences()

    console.log('Store状态已持久化')

  } catch (error) {
    console.warn('Store状态持久化失败:', error)
  }
}

/**
 * 恢复Store状态 (简化版)
 * 从本地存储恢复状态
 */
export function restoreStoreState() {
  try {
    const userStore = useUserStore()
    // 恢复用户偏好设置
    userStore.loadUserPreferences()
    console.log('Store状态已恢复')

  } catch (error) {
    console.warn('Store状态恢复失败:', error)
  }
}

/**
 * 开发环境调试工具
 */
if (import.meta.env.DEV) {
  // 暴露到全局以便调试
  window.getAppStateSummary = getAppStateSummary
  window.resetAllStores = resetAllStores
  window.persistStoreState = persistStoreState
  window.restoreStoreState = restoreStoreState
  
  // 定期输出状态摘要（开发模式）
  setInterval(() => {
    console.log('应用状态摘要:', getAppStateSummary())
  }, 30000) // 每30秒输出一次
}
