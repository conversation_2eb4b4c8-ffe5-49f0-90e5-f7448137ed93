/**
 * 统一的主数据store
 * 直接映射后端SystemStateData结构，实现基于WebSocket的数据驱动架构
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

export const useMainStore = defineStore('main', () => {
  const router = useRouter()

  // 直接映射后端SystemStateData的响应式数据
  const currentState = ref('IDLE')
  const userInfo = ref(null)
  const currentAction = ref(null)
  const actionList = ref([])
  const message = ref('')
  const actionTemplates = ref({})
  const statistics = ref(null)
  const sessionId = ref(null)
  
  // 扩展字段（前端需要但后端SystemStateData中没有的）
  const progressInfo = ref(null)
  const trainingSession = ref(null)
  const poseKeypoints = ref([])
  
  // WebSocket连接状态
  const isConnected = ref(false)
  const connectionError = ref(null)

  // 计算属性
  const isUserLoggedIn = computed(() => {
    return userInfo.value && userInfo.value.patient_id
  })

  const currentActionName = computed(() => {
    return currentAction.value ? currentAction.value.action_type : null
  })

  const trainingProgress = computed(() => {
    if (!currentAction.value) return 0
    const { current_set = 0, sets = 1, current_rep = 0, reps_per_set = 1 } = currentAction.value
    const totalReps = sets * reps_per_set
    const completedReps = (current_set - 1) * reps_per_set + current_rep
    return Math.min(100, Math.round((completedReps / totalReps) * 100))
  })

  /**
   * 统一的WebSocket消息处理器
   * 根据message_type分发处理不同类型的消息
   */
  const handleMessage = (message) => {
    try {
      const { message_type, data, session_id, timestamp } = message
      
      // 更新session信息
      if (session_id && !sessionId.value) {
        sessionId.value = session_id
        console.log('设置会话ID:', session_id)
      }

      // 根据消息类型处理数据
      switch (message_type) {
        case 'user_detected':
          handleUserDetected(data)
          break
        case 'login_success':
          handleLoginSuccess(data)
          break
        case 'pose_data':
          handlePoseData(data)
          break
        case 'action_ready':
          handleActionReady(data)
          break
        case 'training_info':
          handleTrainingInfo(data)
          break
        case 'rest_time':
          handleRestTime(data)
          break
        case 'action_changed':
          handleActionChanged(data)
          break
        case 'training_completed':
          handleTrainingCompleted(data)
          break
        case 'training_session_ended':
          handleTrainingSessionEnded(data)
          break
        case 'system_state':
          handleSystemState(data)
          break
        default:
          console.warn('未知的消息类型:', message_type, data)
      }
    } catch (error) {
      console.error('处理WebSocket消息失败:', error, message)
      connectionError.value = `消息处理错误: ${error.message}`
    }
  }

  /**
   * 处理用户检测消息
   */
  const handleUserDetected = (data) => {
    console.log('收到用户检测消息:', data)
    
    // 更新系统状态
    if (data.current_state) {
      currentState.value = data.current_state
    }
    
    // 更新用户信息
    if (data.user_info) {
      userInfo.value = data.user_info
    }
    
    // 更新消息
    if (data.message) {
      message.value = data.message
    }
    
    // 更新进度信息（如果有）
    if (data.progress_info) {
      progressInfo.value = data.progress_info
    }
    
    // 显示检测消息
    if (data.user_info && data.user_info.patient_id) {
      ElMessage.info(`检测到用户 ${data.user_info.patient_id}，正在识别...`)
    }
  }

  /**
   * 处理登录成功消息
   */
  const handleLoginSuccess = (data) => {
    console.log('收到登录成功消息:', data)
    
    // 更新所有相关数据
    if (data.current_state) currentState.value = data.current_state
    if (data.user_info) userInfo.value = data.user_info
    if (data.current_action) currentAction.value = data.current_action
    if (data.action_list) actionList.value = data.action_list
    if (data.action_templates) actionTemplates.value = data.action_templates
    if (data.message) message.value = data.message
    if (data.progress_info) progressInfo.value = data.progress_info
    
    // 显示欢迎消息
    if (data.user_info && data.user_info.name) {
      ElMessage.success(`欢迎 ${data.user_info.name}！任务已加载，即将进入训练`)
      
      // 3秒后跳转到训练页面
      setTimeout(() => {
        router.push('/training')
      }, 3000)
    }
  }

  /**
   * 处理姿态数据消息
   */
  const handlePoseData = (data) => {
    // 更新姿态关键点数据（用于实时绘制）
    if (data.pose_keypoints) {
      poseKeypoints.value = data.pose_keypoints
    }
    
    // 更新当前动作的关键点数据
    if (data.key_keypoints && currentAction.value) {
      currentAction.value.key_keypoints = data.key_keypoints
    }
  }

  /**
   * 处理动作准备就绪消息
   */
  const handleActionReady = (data) => {
    console.log('收到动作准备就绪消息:', data)
    
    if (data.current_state) currentState.value = data.current_state
    if (data.current_action) currentAction.value = data.current_action
    if (data.message) message.value = data.message
    
    // 显示准备完成消息和倒计时
    ElMessage.success('准备动作完成！3秒后开始训练')
  }

  /**
   * 处理训练信息消息
   */
  const handleTrainingInfo = (data) => {
    // 更新训练进度
    if (data.current_action) currentAction.value = data.current_action
    if (data.progress_info) progressInfo.value = data.progress_info
    if (data.current_state) currentState.value = data.current_state
    if (data.message) message.value = data.message
  }

  /**
   * 处理休息时间消息
   */
  const handleRestTime = (data) => {
    console.log('进入休息阶段:', data)
    
    if (data.progress_info) progressInfo.value = data.progress_info
    if (data.message) message.value = data.message
    
    // 显示休息提示
    ElMessage.info('进入休息阶段，请稍作休息')
  }

  /**
   * 处理动作切换消息
   */
  const handleActionChanged = (data) => {
    console.log('动作切换:', data)
    
    if (data.current_action) currentAction.value = data.current_action
    if (data.current_state) currentState.value = data.current_state
    if (data.message) message.value = data.message
    
    // 显示动作切换消息
    if (data.current_action && data.current_action.action_type) {
      ElMessage.info(`切换到新动作: ${data.current_action.action_type}`)
    }
  }

  /**
   * 处理训练完成消息
   */
  const handleTrainingCompleted = (data) => {
    console.log('训练完成:', data)
    
    if (data.training_session) trainingSession.value = data.training_session
    if (data.current_state) currentState.value = data.current_state
    if (data.message) message.value = data.message
    
    // 显示完成消息并跳转到报告页面
    ElMessage.success('训练完成！正在生成报告...')
    
    setTimeout(() => {
      router.push('/report')
    }, 2000)
  }

  /**
   * 处理训练会话结束消息
   */
  const handleTrainingSessionEnded = (data) => {
    console.log('训练会话结束:', data)
    
    if (data.training_session) trainingSession.value = data.training_session
    if (data.message) message.value = data.message
  }

  /**
   * 处理系统状态消息
   */
  const handleSystemState = (data) => {
    if (data.current_state) currentState.value = data.current_state
    if (data.message) message.value = data.message
  }

  /**
   * 重置所有数据
   */
  const resetData = () => {
    currentState.value = 'IDLE'
    userInfo.value = null
    currentAction.value = null
    actionList.value = []
    message.value = ''
    actionTemplates.value = {}
    statistics.value = null
    sessionId.value = null
    progressInfo.value = null
    trainingSession.value = null
    poseKeypoints.value = []
    connectionError.value = null
  }

  /**
   * 设置连接状态
   */
  const setConnectionStatus = (connected, error = null) => {
    isConnected.value = connected
    connectionError.value = error
  }

  return {
    // 响应式数据
    currentState,
    userInfo,
    currentAction,
    actionList,
    message,
    actionTemplates,
    statistics,
    sessionId,
    progressInfo,
    trainingSession,
    poseKeypoints,
    isConnected,
    connectionError,
    
    // 计算属性
    isUserLoggedIn,
    currentActionName,
    trainingProgress,
    
    // 方法
    handleMessage,
    resetData,
    setConnectionStatus
  }
})
