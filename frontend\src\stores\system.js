/**
 * 系统状态管理 Store (简化版)
 * 保留基础的连接管理功能，其他功能已迁移到主store
 *
 * 注意：大部分功能已迁移到 stores/main.js
 * 此文件仅保留向后兼容性和基础连接管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useMainStore } from '@/stores/main'
import websocketService from '@/services/websocket'

export const useSystemStore = defineStore('system', () => {
  // 获取主store引用
  const mainStore = useMainStore()

  // 基础连接状态 (从主store获取)
  const isConnected = computed(() => mainStore.isConnected)
  const connectionError = computed(() => mainStore.connectionError)
  const currentState = computed(() => mainStore.currentState)
  const sessionId = computed(() => mainStore.sessionId)

  // 系统信息
  const systemInfo = ref({
    version: '1.0.0',
    buildTime: null,
    environment: import.meta.env.MODE
  })

  // 简化的计算属性 (向后兼容)
  const connectionStatus = computed(() => {
    if (!isConnected.value) return 'disconnected'
    if (connectionError.value) return 'error'
    return 'connected'
  })

  const isSystemReady = computed(() => {
    return isConnected.value && currentState.value !== 'IDLE'
  })

  // 简化的方法 (向后兼容)

  /**
   * 连接WebSocket (委托给主store)
   */
  async function connectWebSocket() {
    try {
      console.log('系统store: 委托WebSocket连接到主store')
      await websocketService.connect()
    } catch (error) {
      console.error('WebSocket连接失败:', error)
      throw error
    }
  }

  /**
   * 断开WebSocket连接
   */
  function disconnectWebSocket() {
    console.log('系统store: 断开WebSocket连接')
    websocketService.disconnect()
  }

  /**
   * 获取系统状态摘要 (向后兼容)
   */
  function getSystemSummary() {
    return {
      state: currentState.value,
      connection: connectionStatus.value,
      isConnected: isConnected.value,
      sessionId: sessionId.value
    }
  }

  // 返回简化的API (向后兼容)
  return {
    // 基础状态 (从主store获取)
    currentState,
    isConnected,
    connectionError,
    sessionId,

    // 系统信息
    systemInfo,

    // 计算属性
    isSystemReady,
    connectionStatus,

    // 基础方法
    connectWebSocket,
    disconnectWebSocket,
    getSystemSummary
  }
})

// 向后兼容的导出
export default useSystemStore
