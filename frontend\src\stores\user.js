/**
 * 用户状态管理 Store (简化版)
 * 保留基础的用户信息管理，其他功能已迁移到主store
 *
 * 注意：大部分功能已迁移到 stores/main.js
 * 此文件仅保留向后兼容性和基础用户管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useMainStore } from '@/stores/main'

export const useUserStore = defineStore('user', () => {
  // 获取主store引用
  const mainStore = useMainStore()

  // 用户基本信息 (从主store获取)
  const userInfo = computed(() => mainStore.userInfo)
  const isAuthenticated = computed(() => mainStore.isUserLoggedIn)

  // 本地状态 (向后兼容)
  const userStatus = ref('offline')
  const lastLoginTime = ref(null)

  // 用户偏好设置 (保留)
  const preferences = ref({
    language: 'zh-CN',
    theme: 'light',
    soundEnabled: true,
    voiceGuidance: true,
    difficulty: 'medium'
  })

  // 简化的计算属性 (向后兼容)
  const userDisplayName = computed(() => {
    if (!userInfo.value) return '未登录用户'
    return userInfo.value.name || userInfo.value.patient_id || '用户'
  })

  const isUserLoggedIn = computed(() => {
    return isAuthenticated.value
  })

  // 简化的方法 (向后兼容)

  /**
   * 加载用户偏好设置
   */
  function loadUserPreferences() {
    try {
      const saved = localStorage.getItem('user-preferences')
      if (saved) {
        const savedPrefs = JSON.parse(saved)
        preferences.value = { ...preferences.value, ...savedPrefs }
      }
    } catch (error) {
      console.warn('加载用户偏好失败:', error)
    }
  }

  /**
   * 保存用户偏好设置
   */
  function saveUserPreferences() {
    try {
      localStorage.setItem('user-preferences', JSON.stringify(preferences.value))
    } catch (error) {
      console.warn('保存用户偏好失败:', error)
    }
  }



  // 返回简化的API (向后兼容)
  return {
    // 基础状态 (从主store获取)
    userInfo,
    isAuthenticated,

    // 本地状态
    userStatus,
    lastLoginTime,
    preferences,

    // 计算属性
    userDisplayName,
    isUserLoggedIn,

    // 方法
    loadUserPreferences,
    saveUserPreferences
  }
})