/**
 * 关键点可视化渲染引擎
 * 支持动态数量关键点的实时渲染，基于WebSocket接收的required_keypoints数据
 */

import { getSkeletonConnections } from './skeletonConnections'

export class KeypointRenderer {
  constructor(canvas, options = {}) {
    this.canvas = canvas
    this.ctx = canvas.getContext('2d')
    this.options = {
      pointRadius: 4,
      pointColor: '#ff4444',
      lineColor: '#00ff00',
      lineWidth: 2,
      confidenceThreshold: 0.3,
      showConfidence: false,
      enableSmoothing: true,
      maxFPS: 60,
      ...options
    }
    
    // 渲染状态
    this.isRendering = false
    this.lastFrameTime = 0
    this.frameInterval = 1000 / this.options.maxFPS
    this.animationId = null
    
    // 数据缓存
    this.currentKeypoints = []
    this.requiredKeypoints = []
    this.smoothedKeypoints = []
    this.lastUpdateTime = 0
    
    // 坐标转换参数
    this.scale = 1
    this.offsetX = 0
    this.offsetY = 0
    
    // 性能优化
    this.offscreenCanvas = null
    this.offscreenCtx = null
    this.needsRedraw = true
    
    // 初始化
    this.initializeRenderer()
  }

  /**
   * 初始化渲染器
   */
  initializeRenderer() {
    // 创建离屏Canvas用于双缓冲
    this.createOffscreenCanvas()
    
    // 设置Canvas样式
    this.setupCanvasStyles()
    
    // 绑定事件
    this.bindEvents()
    
    console.log('关键点渲染器初始化完成')
  }

  /**
   * 创建离屏Canvas
   */
  createOffscreenCanvas() {
    this.offscreenCanvas = document.createElement('canvas')
    this.offscreenCanvas.width = this.canvas.width
    this.offscreenCanvas.height = this.canvas.height
    this.offscreenCtx = this.offscreenCanvas.getContext('2d')
  }

  /**
   * 设置Canvas样式
   */
  setupCanvasStyles() {
    this.ctx.lineCap = 'round'
    this.ctx.lineJoin = 'round'
    this.offscreenCtx.lineCap = 'round'
    this.offscreenCtx.lineJoin = 'round'
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    // 监听Canvas尺寸变化
    const resizeObserver = new ResizeObserver(() => {
      this.handleResize()
    })
    resizeObserver.observe(this.canvas)
  }

  /**
   * 处理Canvas尺寸变化
   */
  handleResize() {
    const rect = this.canvas.getBoundingClientRect()
    const dpr = window.devicePixelRatio || 1
    
    // 设置Canvas实际尺寸
    this.canvas.width = rect.width * dpr
    this.canvas.height = rect.height * dpr
    
    // 设置CSS尺寸
    this.canvas.style.width = rect.width + 'px'
    this.canvas.style.height = rect.height + 'px'
    
    // 缩放上下文以适应设备像素比
    this.ctx.scale(dpr, dpr)
    
    // 重新创建离屏Canvas
    this.createOffscreenCanvas()
    this.offscreenCtx.scale(dpr, dpr)
    
    // 重新计算坐标转换参数
    this.updateTransform()
    
    this.needsRedraw = true
  }

  /**
   * 更新关键点数据
   * @param {Array} keypoints - 关键点数组 [[x, y, confidence], ...]
   * @param {Array} requiredIndices - 需要渲染的关键点索引
   */
  updateKeypoints(keypoints, requiredIndices = []) {
    if (!Array.isArray(keypoints) || keypoints.length === 0) {
      return
    }

    this.currentKeypoints = keypoints
    this.requiredKeypoints = requiredIndices
    this.lastUpdateTime = Date.now()

    // 应用平滑处理
    if (this.options.enableSmoothing) {
      this.applySmoothing()
    } else {
      this.smoothedKeypoints = [...keypoints]
    }

    // 更新坐标转换
    this.updateTransform()
    
    this.needsRedraw = true
    
    // 开始渲染循环
    if (!this.isRendering) {
      this.startRenderLoop()
    }
  }

  /**
   * 应用平滑处理
   */
  applySmoothing() {
    const smoothingFactor = 0.7
    
    if (this.smoothedKeypoints.length === 0) {
      this.smoothedKeypoints = [...this.currentKeypoints]
      return
    }

    this.smoothedKeypoints = this.currentKeypoints.map((point, index) => {
      if (index >= this.smoothedKeypoints.length) {
        return point
      }

      const [x, y, confidence] = point
      const [prevX, prevY, prevConf] = this.smoothedKeypoints[index]

      // 只对置信度足够的点进行平滑
      if (confidence > this.options.confidenceThreshold) {
        return [
          prevX * smoothingFactor + x * (1 - smoothingFactor),
          prevY * smoothingFactor + y * (1 - smoothingFactor),
          confidence
        ]
      }

      return point
    })
  }

  /**
   * 更新坐标转换参数
   */
  updateTransform() {
    if (this.smoothedKeypoints.length === 0) return

    // 获取需要渲染的关键点
    const visibleKeypoints = this.getVisibleKeypoints()
    if (visibleKeypoints.length === 0) return

    // 计算边界框
    const bounds = this.calculateBounds(visibleKeypoints)
    
    // 计算缩放和偏移
    const canvasWidth = this.canvas.width / (window.devicePixelRatio || 1)
    const canvasHeight = this.canvas.height / (window.devicePixelRatio || 1)
    
    const padding = 50
    const scaleX = (canvasWidth - padding * 2) / bounds.width
    const scaleY = (canvasHeight - padding * 2) / bounds.height
    
    this.scale = Math.min(scaleX, scaleY, 1) // 不放大，只缩小
    this.offsetX = (canvasWidth - bounds.width * this.scale) / 2 - bounds.minX * this.scale
    this.offsetY = (canvasHeight - bounds.height * this.scale) / 2 - bounds.minY * this.scale
  }

  /**
   * 获取可见的关键点
   */
  getVisibleKeypoints() {
    if (this.requiredKeypoints.length === 0) {
      return this.smoothedKeypoints.filter(point => 
        point[2] > this.options.confidenceThreshold
      )
    }

    return this.requiredKeypoints
      .map(index => this.smoothedKeypoints[index])
      .filter(point => point && point[2] > this.options.confidenceThreshold)
  }

  /**
   * 计算关键点边界框
   */
  calculateBounds(keypoints) {
    if (keypoints.length === 0) {
      return { minX: 0, minY: 0, maxX: 100, maxY: 100, width: 100, height: 100 }
    }

    let minX = Infinity, minY = Infinity
    let maxX = -Infinity, maxY = -Infinity

    keypoints.forEach(([x, y]) => {
      minX = Math.min(minX, x)
      minY = Math.min(minY, y)
      maxX = Math.max(maxX, x)
      maxY = Math.max(maxY, y)
    })

    return {
      minX,
      minY,
      maxX,
      maxY,
      width: maxX - minX,
      height: maxY - minY
    }
  }

  /**
   * 开始渲染循环
   */
  startRenderLoop() {
    if (this.isRendering) return

    this.isRendering = true
    this.renderFrame()
  }

  /**
   * 停止渲染循环
   */
  stopRenderLoop() {
    this.isRendering = false
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
      this.animationId = null
    }
  }

  /**
   * 渲染单帧
   */
  renderFrame() {
    if (!this.isRendering) return

    const now = Date.now()
    
    // 帧率控制
    if (now - this.lastFrameTime >= this.frameInterval) {
      if (this.needsRedraw) {
        this.render()
        this.needsRedraw = false
      }
      this.lastFrameTime = now
    }

    this.animationId = requestAnimationFrame(() => this.renderFrame())
  }

  /**
   * 执行渲染
   */
  render() {
    // 清空离屏Canvas
    this.offscreenCtx.clearRect(0, 0, this.offscreenCanvas.width, this.offscreenCanvas.height)

    const visibleKeypoints = this.getVisibleKeypoints()
    if (visibleKeypoints.length === 0) return

    // 绘制骨架连线
    this.drawSkeleton(visibleKeypoints)
    
    // 绘制关键点
    this.drawKeypoints(visibleKeypoints)

    // 将离屏Canvas内容复制到主Canvas
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height)
    this.ctx.drawImage(this.offscreenCanvas, 0, 0)
  }

  /**
   * 绘制骨架连线
   */
  drawSkeleton(keypoints) {
    const connections = getSkeletonConnections(this.requiredKeypoints)
    
    this.offscreenCtx.strokeStyle = this.options.lineColor
    this.offscreenCtx.lineWidth = this.options.lineWidth
    this.offscreenCtx.beginPath()

    connections.forEach(([startIdx, endIdx]) => {
      const startPoint = keypoints[startIdx]
      const endPoint = keypoints[endIdx]

      if (startPoint && endPoint && 
          startPoint[2] > this.options.confidenceThreshold &&
          endPoint[2] > this.options.confidenceThreshold) {
        
        const [x1, y1] = this.transformPoint(startPoint[0], startPoint[1])
        const [x2, y2] = this.transformPoint(endPoint[0], endPoint[1])

        this.offscreenCtx.moveTo(x1, y1)
        this.offscreenCtx.lineTo(x2, y2)
      }
    })

    this.offscreenCtx.stroke()
  }

  /**
   * 绘制关键点
   */
  drawKeypoints(keypoints) {
    keypoints.forEach((point, index) => {
      if (point[2] > this.options.confidenceThreshold) {
        const [x, y] = this.transformPoint(point[0], point[1])
        
        // 绘制关键点
        this.offscreenCtx.fillStyle = this.options.pointColor
        this.offscreenCtx.beginPath()
        this.offscreenCtx.arc(x, y, this.options.pointRadius, 0, Math.PI * 2)
        this.offscreenCtx.fill()

        // 绘制置信度（可选）
        if (this.options.showConfidence) {
          this.drawConfidence(x, y, point[2])
        }
      }
    })
  }

  /**
   * 绘制置信度文本
   */
  drawConfidence(x, y, confidence) {
    this.offscreenCtx.fillStyle = '#ffffff'
    this.offscreenCtx.font = '10px Arial'
    this.offscreenCtx.textAlign = 'center'
    this.offscreenCtx.fillText(
      confidence.toFixed(2), 
      x, 
      y - this.options.pointRadius - 5
    )
  }

  /**
   * 坐标转换
   */
  transformPoint(x, y) {
    return [
      x * this.scale + this.offsetX,
      y * this.scale + this.offsetY
    ]
  }

  /**
   * 清空Canvas
   */
  clear() {
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height)
    this.offscreenCtx.clearRect(0, 0, this.offscreenCanvas.width, this.offscreenCanvas.height)
    this.needsRedraw = false
  }

  /**
   * 更新渲染选项
   */
  updateOptions(newOptions) {
    this.options = { ...this.options, ...newOptions }
    this.frameInterval = 1000 / this.options.maxFPS
    this.needsRedraw = true
  }

  /**
   * 销毁渲染器
   */
  destroy() {
    this.stopRenderLoop()
    this.clear()
    this.currentKeypoints = []
    this.smoothedKeypoints = []
    this.requiredKeypoints = []
  }

  /**
   * 获取渲染统计信息
   */
  getStats() {
    return {
      isRendering: this.isRendering,
      keypointCount: this.currentKeypoints.length,
      visibleKeypointCount: this.getVisibleKeypoints().length,
      lastUpdateTime: this.lastUpdateTime,
      scale: this.scale,
      fps: Math.round(1000 / this.frameInterval)
    }
  }
}
