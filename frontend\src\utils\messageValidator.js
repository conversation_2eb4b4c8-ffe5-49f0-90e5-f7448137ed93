/**
 * WebSocket消息格式验证工具
 * 验证从后端接收的消息格式是否符合预期
 */

/**
 * 支持的消息类型列表
 */
export const MESSAGE_TYPES = {
  SYSTEM_STATE: 'system_state',
  USER_DETECTED: 'user_detected', 
  LOGIN_SUCCESS: 'login_success',
  ACTION_READY: 'action_ready',
  TRAINING_INFO: 'training_info',
  REST_TIME: 'rest_time',
  TRAINING_COMPLETED: 'training_completed',
  TRAINING_SESSION_ENDED: 'training_session_ended'
}

/**
 * 验证WebSocket消息基本格式
 * @param {object} message - 消息对象
 * @returns {boolean} 验证结果
 */
export function validateWebSocketMessage(message) {
  if (!message || typeof message !== 'object') {
    console.warn('消息不是有效对象')
    return false
  }

  // 检查必需字段
  const requiredFields = ['message_type', 'timestamp', 'data']
  for (const field of requiredFields) {
    if (!(field in message)) {
      console.warn(`消息缺少必需字段: ${field}`)
      return false
    }
  }

  // 验证消息类型
  if (!Object.values(MESSAGE_TYPES).includes(message.message_type)) {
    console.warn(`不支持的消息类型: ${message.message_type}`)
    return false
  }

  // 验证时间戳
  if (typeof message.timestamp !== 'number' || message.timestamp <= 0) {
    console.warn('无效的时间戳')
    return false
  }

  // 验证数据字段
  if (message.data === null || message.data === undefined) {
    console.warn('消息数据字段为空')
    return false
  }

  return true
}

/**
 * 验证系统状态消息
 * @param {object} data - 消息数据
 * @returns {boolean} 验证结果
 */
export function validateSystemStateData(data) {
  if (!data || typeof data !== 'object') {
    return false
  }

  // 检查当前状态字段
  if (!data.current_state || typeof data.current_state !== 'string') {
    console.warn('系统状态消息缺少有效的current_state字段')
    return false
  }

  return true
}

/**
 * 验证用户登录成功消息
 * @param {object} data - 消息数据
 * @returns {boolean} 验证结果
 */
export function validateLoginSuccessData(data) {
  if (!data || typeof data !== 'object') {
    return false
  }

  // 检查用户信息
  if (!data.user_info || typeof data.user_info !== 'object') {
    console.warn('登录成功消息缺少用户信息')
    return false
  }

  const userInfo = data.user_info
  if (!userInfo.patient_id || !userInfo.name) {
    console.warn('用户信息缺少必需字段')
    return false
  }

  // 检查任务列表
  if (!data.task_progress || !Array.isArray(data.task_progress)) {
    console.warn('登录成功消息缺少任务进度信息')
    return false
  }

  return true
}

/**
 * 验证训练信息消息
 * @param {object} data - 消息数据
 * @returns {boolean} 验证结果
 */
export function validateTrainingInfoData(data) {
  if (!data || typeof data !== 'object') {
    return false
  }

  // 检查训练信息字段
  if (data.training_info && typeof data.training_info === 'object') {
    const trainingInfo = data.training_info
    
    // 验证基本训练信息
    if (trainingInfo.action_type && 
        typeof trainingInfo.current_set === 'number' &&
        typeof trainingInfo.current_rep === 'number') {
      return true
    }
  }

  // 检查关键点数据
  if (data.keypoint_data && Array.isArray(data.keypoint_data)) {
    return true
  }

  // 检查动作识别结果
  if (data.action_recognized !== undefined) {
    return true
  }

  console.warn('训练信息消息格式无效')
  return false
}

/**
 * 验证关键点数据
 * @param {array} keypoints - 关键点数组
 * @returns {boolean} 验证结果
 */
export function validateKeypointData(keypoints) {
  if (!Array.isArray(keypoints)) {
    console.warn('关键点数据不是数组')
    return false
  }

  // 检查每个关键点的格式
  for (let i = 0; i < keypoints.length; i++) {
    const point = keypoints[i]
    
    if (!Array.isArray(point) || point.length !== 3) {
      console.warn(`关键点${i}格式错误，应为[x, y, confidence]`)
      return false
    }
    
    const [x, y, confidence] = point
    if (typeof x !== 'number' || typeof y !== 'number' || typeof confidence !== 'number') {
      console.warn(`关键点${i}坐标或置信度不是数字`)
      return false
    }
    
    if (confidence < 0 || confidence > 1) {
      console.warn(`关键点${i}置信度超出范围[0,1]`)
      return false
    }
  }

  return true
}

/**
 * 验证训练报告数据
 * @param {object} data - 报告数据
 * @returns {boolean} 验证结果
 */
export function validateTrainingReportData(data) {
  if (!data || typeof data !== 'object') {
    return false
  }

  // 检查必需字段
  const requiredFields = ['report_id', 'user_id', 'generation_time']
  for (const field of requiredFields) {
    if (!data[field]) {
      console.warn(`训练报告缺少字段: ${field}`)
      return false
    }
  }

  // 检查性能数据
  if (data.performance && typeof data.performance === 'object') {
    return true
  }

  // 检查动作分析数据
  if (data.actions_analysis && typeof data.actions_analysis === 'object') {
    return true
  }

  return true
}

/**
 * 根据消息类型验证数据
 * @param {string} messageType - 消息类型
 * @param {object} data - 消息数据
 * @returns {boolean} 验证结果
 */
export function validateMessageData(messageType, data) {
  switch (messageType) {
    case MESSAGE_TYPES.SYSTEM_STATE:
      return validateSystemStateData(data)
    
    case MESSAGE_TYPES.LOGIN_SUCCESS:
      return validateLoginSuccessData(data)
    
    case MESSAGE_TYPES.TRAINING_INFO:
      return validateTrainingInfoData(data)
    
    case MESSAGE_TYPES.TRAINING_COMPLETED:
      return validateTrainingReportData(data)
    
    case MESSAGE_TYPES.USER_DETECTED:
    case MESSAGE_TYPES.ACTION_READY:
    case MESSAGE_TYPES.REST_TIME:
    case MESSAGE_TYPES.TRAINING_SESSION_ENDED:
      // 这些消息类型的数据验证相对简单
      return data !== null && data !== undefined
    
    default:
      console.warn(`未知消息类型: ${messageType}`)
      return false
  }
}

/**
 * 创建标准WebSocket消息格式
 * @param {string} messageType - 消息类型
 * @param {object} data - 消息数据
 * @param {string} sessionId - 会话ID（可选）
 * @returns {object} 标准消息对象
 */
export function createWebSocketMessage(messageType, data, sessionId = null) {
  return {
    message_type: messageType,
    timestamp: Date.now(),
    data: data,
    session_id: sessionId
  }
}

/**
 * 消息验证器类
 */
export class MessageValidator {
  constructor() {
    this.validationErrors = []
  }

  /**
   * 验证消息并记录错误
   * @param {object} message - 消息对象
   * @returns {boolean} 验证结果
   */
  validate(message) {
    this.validationErrors = []

    if (!validateWebSocketMessage(message)) {
      this.validationErrors.push('基本消息格式验证失败')
      return false
    }

    if (!validateMessageData(message.message_type, message.data)) {
      this.validationErrors.push(`消息数据验证失败: ${message.message_type}`)
      return false
    }

    return true
  }

  /**
   * 获取验证错误
   * @returns {array} 错误列表
   */
  getErrors() {
    return [...this.validationErrors]
  }

  /**
   * 清除验证错误
   */
  clearErrors() {
    this.validationErrors = []
  }
}

// 导出默认验证器实例
export const messageValidator = new MessageValidator()
