/**
 * 骨架连接关系定义
 * 定义RTMPose 133个关键点的骨架连接关系，支持动态关键点渲染
 */
/**
 * RTMPose 133关键点的完整骨架连接定义
 * 基于COCO-WholeBody格式，包含身体、面部、手部关键点
 */
export const FULL_SKELETON_CONNECTIONS = [
  // 身体骨架连接 (COCO 17个关键点)
  [0, 1],   // 鼻子 -> 左眼
  [0, 2],   // 鼻子 -> 右眼
  [1, 3],   // 左眼 -> 左耳
  [2, 4],   // 右眼 -> 右耳
  [5, 6],   // 左肩 -> 右肩
  [5, 7],   // 左肩 -> 左肘
  [7, 9],   // 左肘 -> 左腕
  [6, 8],   // 右肩 -> 右肘
  [8, 10],  // 右肘 -> 右腕
  [5, 11],  // 左肩 -> 左髋
  [6, 12],  // 右肩 -> 右髋
  [11, 12], // 左髋 -> 右髋
  [11, 13], // 左髋 -> 左膝
  [13, 15], // 左膝 -> 左踝
  [12, 14], // 右髋 -> 右膝
  [14, 16], // 右膝 -> 右踝

  // 面部关键点连接 (68个关键点，索引17-84)
  // 面部轮廓
  [17, 18], [18, 19], [19, 20], [20, 21], [21, 22], [22, 23], [23, 24], [24, 25], [25, 26],
  
  // 左眉毛
  [27, 28], [28, 29], [29, 30], [30, 31],
  
  // 右眉毛
  [32, 33], [33, 34], [34, 35], [35, 36],
  
  // 鼻梁
  [37, 38], [38, 39], [39, 40],
  
  // 鼻孔
  [41, 42], [42, 43], [43, 44], [44, 45],
  
  // 左眼
  [46, 47], [47, 48], [48, 49], [49, 50], [50, 51], [51, 46],
  
  // 右眼
  [52, 53], [53, 54], [54, 55], [55, 56], [56, 57], [57, 52],
  
  // 嘴唇外轮廓
  [58, 59], [59, 60], [60, 61], [61, 62], [62, 63], [63, 64], [64, 65], [65, 66], [66, 67], [67, 58],
  
  // 嘴唇内轮廓
  [68, 69], [69, 70], [70, 71], [71, 72], [72, 73], [73, 74], [74, 75], [75, 76], [76, 77], [77, 68],

  // 左手关键点连接 (21个关键点，索引91-111)
  // 手腕到手指根部
  [91, 92], [91, 96], [91, 100], [91, 104], [91, 108],
  
  // 拇指
  [92, 93], [93, 94], [94, 95],
  
  // 食指
  [96, 97], [97, 98], [98, 99],
  
  // 中指
  [100, 101], [101, 102], [102, 103],
  
  // 无名指
  [104, 105], [105, 106], [106, 107],
  
  // 小指
  [108, 109], [109, 110], [110, 111],

  // 右手关键点连接 (21个关键点，索引112-132)
  // 手腕到手指根部
  [112, 113], [112, 117], [112, 121], [112, 125], [112, 129],
  
  // 拇指
  [113, 114], [114, 115], [115, 116],
  
  // 食指
  [117, 118], [118, 119], [119, 120],
  
  // 中指
  [121, 122], [122, 123], [123, 124],
  
  // 无名指
  [125, 126], [126, 127], [127, 128],
  
  // 小指
  [129, 130], [130, 131], [131, 132]
]

/**
 * 身体主要骨架连接（简化版）
 */
export const BODY_SKELETON_CONNECTIONS = [
  [5, 6],   // 左肩 -> 右肩
  [5, 7],   // 左肩 -> 左肘
  [7, 9],   // 左肘 -> 左腕
  [6, 8],   // 右肩 -> 右肘
  [8, 10],  // 右肘 -> 右腕
  [5, 11],  // 左肩 -> 左髋
  [6, 12],  // 右肩 -> 右髋
  [11, 12], // 左髋 -> 右髋
  [11, 13], // 左髋 -> 左膝
  [13, 15], // 左膝 -> 左踝
  [12, 14], // 右髋 -> 右膝
  [14, 16]  // 右膝 -> 右踝
]

/**
 * 动作特定的关键点连接映射
 */
export const ACTION_SPECIFIC_CONNECTIONS = {
  // 肩部触摸动作
  shoulder_touch: [
    [5, 6],   // 肩部连接
    [5, 7],   // 左肩到左肘
    [7, 9],   // 左肘到左腕
    [6, 8],   // 右肩到右肘
    [8, 10],  // 右肘到右腕
    [0, 5],   // 头部到左肩
    [0, 6]    // 头部到右肩
  ],
  
  // 手臂举起动作
  arm_raise: [
    [5, 7],   // 左肩到左肘
    [7, 9],   // 左肘到左腕
    [6, 8],   // 右肩到右肘
    [8, 10],  // 右肘到右腕
    [5, 6],   // 肩部连接
    [5, 11],  // 左肩到左髋
    [6, 12]   // 右肩到右髋
  ],
  
  // 手指触摸动作
  finger_touch: [
    // 左手连接
    [91, 92], [92, 93], [93, 94], [94, 95],  // 拇指
    [91, 96], [96, 97], [97, 98], [98, 99],  // 食指
    [91, 100], [100, 101], [101, 102], [102, 103], // 中指
    // 右手连接
    [112, 113], [113, 114], [114, 115], [115, 116], // 拇指
    [112, 117], [117, 118], [118, 119], [119, 120], // 食指
    [112, 121], [121, 122], [122, 123], [123, 124], // 中指
    // 手腕到肘部
    [9, 91],   // 左腕到左手
    [10, 112]  // 右腕到右手
  ],
  
  // 手掌翻转动作
  palm_flip: [
    [7, 9],   // 左肘到左腕
    [8, 10],  // 右肘到右腕
    [9, 91],  // 左腕到左手
    [10, 112], // 右腕到右手
    // 手掌关键连接
    [91, 96], [91, 100], [91, 104], [91, 108], // 左手腕到手指
    [112, 117], [112, 121], [112, 125], [112, 129] // 右手腕到手指
  ]
}

/**
 * 关键点名称映射（用于调试和显示）
 */
export const KEYPOINT_NAMES = {
  // 身体关键点 (0-16)
  0: '鼻子', 1: '左眼', 2: '右眼', 3: '左耳', 4: '右耳',
  5: '左肩', 6: '右肩', 7: '左肘', 8: '右肘', 9: '左腕', 10: '右腕',
  11: '左髋', 12: '右髋', 13: '左膝', 14: '右膝', 15: '左踝', 16: '右踝',
  
  // 面部关键点 (17-84) - 简化显示
  17: '面部轮廓1', 84: '面部轮廓68',
  
  // 左手关键点 (91-111)
  91: '左手腕', 92: '左拇指1', 95: '左拇指4',
  96: '左食指1', 99: '左食指4',
  100: '左中指1', 103: '左中指4',
  
  // 右手关键点 (112-132)
  112: '右手腕', 113: '右拇指1', 116: '右拇指4',
  117: '右食指1', 120: '右食指4',
  121: '右中指1', 124: '右中指4'
}

/**
 * 根据需要的关键点索引获取对应的骨架连接
 * @param {Array} requiredKeypoints - 需要显示的关键点索引数组
 * @param {string} actionType - 动作类型（可选）
 * @returns {Array} 骨架连接数组
 */
export function getSkeletonConnections(requiredKeypoints = [], actionType = null) {
  if (requiredKeypoints.length === 0) {
    return BODY_SKELETON_CONNECTIONS // 默认返回身体骨架
  }

  // 如果指定了动作类型，优先使用动作特定连接
  if (actionType && ACTION_SPECIFIC_CONNECTIONS[actionType]) {
    return filterConnectionsByKeypoints(
      ACTION_SPECIFIC_CONNECTIONS[actionType], 
      requiredKeypoints
    )
  }

  // 根据关键点范围智能选择连接
  const connections = []
  
  // 检查是否包含身体关键点
  if (hasBodyKeypoints(requiredKeypoints)) {
    connections.push(...BODY_SKELETON_CONNECTIONS)
  }
  
  // 检查是否包含左手关键点
  if (hasLeftHandKeypoints(requiredKeypoints)) {
    connections.push(...getHandConnections('left'))
  }
  
  // 检查是否包含右手关键点
  if (hasRightHandKeypoints(requiredKeypoints)) {
    connections.push(...getHandConnections('right'))
  }
  
  // 过滤出实际存在的关键点连接
  return filterConnectionsByKeypoints(connections, requiredKeypoints)
}

/**
 * 检查是否包含身体关键点
 */
function hasBodyKeypoints(keypoints) {
  return keypoints.some(index => index >= 0 && index <= 16)
}

/**
 * 检查是否包含左手关键点
 */
function hasLeftHandKeypoints(keypoints) {
  return keypoints.some(index => index >= 91 && index <= 111)
}

/**
 * 检查是否包含右手关键点
 */
function hasRightHandKeypoints(keypoints) {
  return keypoints.some(index => index >= 112 && index <= 132)
}

/**
 * 获取手部连接
 */
function getHandConnections(hand) {
  if (hand === 'left') {
    return [
      [91, 92], [91, 96], [91, 100], [91, 104], [91, 108],
      [92, 93], [93, 94], [94, 95],
      [96, 97], [97, 98], [98, 99],
      [100, 101], [101, 102], [102, 103],
      [104, 105], [105, 106], [106, 107],
      [108, 109], [109, 110], [110, 111]
    ]
  } else {
    return [
      [112, 113], [112, 117], [112, 121], [112, 125], [112, 129],
      [113, 114], [114, 115], [115, 116],
      [117, 118], [118, 119], [119, 120],
      [121, 122], [122, 123], [123, 124],
      [125, 126], [126, 127], [127, 128],
      [129, 130], [130, 131], [131, 132]
    ]
  }
}

/**
 * 根据关键点过滤连接
 */
function filterConnectionsByKeypoints(connections, keypoints) {
  const keypointSet = new Set(keypoints)
  
  return connections.filter(([start, end]) => 
    keypointSet.has(start) && keypointSet.has(end)
  )
}

/**
 * 获取关键点的颜色配置
 * @param {number} keypointIndex - 关键点索引
 * @returns {string} 颜色值
 */
export function getKeypointColor(keypointIndex) {
  // 身体关键点 - 蓝色系
  if (keypointIndex >= 0 && keypointIndex <= 16) {
    return '#3b82f6'
  }
  
  // 面部关键点 - 绿色系
  if (keypointIndex >= 17 && keypointIndex <= 84) {
    return '#10b981'
  }
  
  // 左手关键点 - 红色系
  if (keypointIndex >= 91 && keypointIndex <= 111) {
    return '#ef4444'
  }
  
  // 右手关键点 - 紫色系
  if (keypointIndex >= 112 && keypointIndex <= 132) {
    return '#8b5cf6'
  }
  
  return '#6b7280' // 默认灰色
}

/**
 * 获取连接线的颜色配置
 * @param {Array} connection - 连接数组 [start, end]
 * @returns {string} 颜色值
 */
export function getConnectionColor(connection) {
  const [start, end] = connection
  
  // 身体连接 - 绿色
  if ((start <= 16 && end <= 16)) {
    return '#22c55e'
  }
  
  // 手部连接 - 橙色
  if ((start >= 91 && end >= 91)) {
    return '#f97316'
  }
  
  return '#6b7280' // 默认灰色
}

/**
 * 获取动作相关的关键点索引
 * @param {string} actionType - 动作类型
 * @returns {Array} 关键点索引数组
 */
export function getActionKeypoints(actionType) {
  const actionKeypointMap = {
    shoulder_touch: [0, 5, 6, 7, 8, 9, 10],
    arm_raise: [5, 6, 7, 8, 9, 10, 11, 12],
    finger_touch: [9, 10, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124],
    palm_flip: [7, 8, 9, 10, 91, 96, 100, 104, 108, 112, 117, 121, 125, 129]
  }
  
  return actionKeypointMap[actionType] || []
}
