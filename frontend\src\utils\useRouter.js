/**
 * 路由导航组合式API (更新为使用主store)
 * 提供安全的路由导航和状态管理集成
 */

import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useMainStore } from '@/stores/main'
import { ElMessageBox } from 'element-plus'

/**
 * 路由导航组合式API
 * @returns {object} 路由导航相关的方法和状态
 */
export function useNavigation() {
  const router = useRouter()
  const route = useRoute()
  const mainStore = useMainStore()

  // 当前路由信息
  const currentRouteName = computed(() => route.name)
  const currentPath = computed(() => route.path)
  const routeQuery = computed(() => route.query)
  const routeParams = computed(() => route.params)

  // 导航状态 (使用主store)
  const canNavigateToTraining = computed(() => {
    return mainStore.isUserLoggedIn &&
           mainStore.isConnected &&
           ['ACTION_PREPARATION', 'ACTION_TRAINING', 'ACTION_REST'].includes(mainStore.currentState)
  })

  const canNavigateToReport = computed(() => {
    return mainStore.isUserLoggedIn &&
           mainStore.isConnected &&
           ['ACTION_COMPLETED', 'TRAINING_SESSION_ENDED'].includes(mainStore.currentState)
  })

  /**
   * 安全导航到指定路由
   * @param {string|object} to - 目标路由
   * @param {object} options - 导航选项
   */
  async function safePush(to, options = {}) {
    try {
      console.log(`导航到: ${typeof to === 'string' ? to : to.name || to.path}`)

      // 检查连接状态 (使用主store)
      if (!mainStore.isConnected && to !== '/error') {
        console.warn('系统未连接，无法导航')
        await router.push({
          name: 'Error',
          query: { error: 'connection_lost' }
        })
        return false
      }

      await router.push(to)
      return true

    } catch (error) {
      console.error('导航失败:', error)
      // 简化错误处理，不依赖已删除的store方法
      console.error('页面跳转失败:', error.message)

      if (options.fallback) {
        try {
          await router.push(options.fallback)
        } catch (fallbackError) {
          console.error('备用导航也失败:', fallbackError)
        }
      }

      return false
    }
  }

  /**
   * 安全替换当前路由
   * @param {string|object} to - 目标路由
   */
  async function safeReplace(to) {
    try {
      await router.replace(to)
      return true
    } catch (error) {
      console.error('路由替换失败:', error)
      // 简化错误处理
      console.error('页面替换失败:', error.message)
      return false
    }
  }

  /**
   * 安全返回上一页
   */
  function safeBack() {
    try {
      if (window.history.length > 1) {
        router.back()
      } else {
        // 如果没有历史记录，导航到首页
        safePush({ name: 'Login' })
      }
    } catch (error) {
      console.error('返回上一页失败:', error)
      safePush({ name: 'Login' })
    }
  }

  /**
   * 根据系统状态导航到合适的页面
   */
  async function navigateBySystemState() {
    const stateRouteMap = {
      'IDLE': 'Login',
      'WAITING': 'Login',
      'USER_LOGIN': 'Login',
      'ACTION_PREPARATION': 'Training',
      'ACTION_TRAINING': 'Training',
      'ACTION_REST': 'Training',
      'ACTION_COMPLETED': 'Report',
      'TRAINING_SESSION_ENDED': 'Report'
    }

    const targetRoute = stateRouteMap[mainStore.currentState]
    if (targetRoute && targetRoute !== currentRouteName.value) {
      console.log(`根据系统状态 ${mainStore.currentState} 导航到 ${targetRoute}`)
      await safePush({ name: targetRoute })
    }
  }

  /**
   * 导航到登录页面
   */
  async function navigateToLogin(query = {}) {
    await safePush({ 
      name: 'Login', 
      query: { 
        ...query,
        from: currentRouteName.value 
      } 
    })
  }

  /**
   * 导航到训练页面
   */
  async function navigateToTraining() {
    if (!canNavigateToTraining.value) {
      console.warn('当前状态不允许进入训练页面')
      console.error('导航失败: 当前状态不允许进入训练页面')
      return false
    }

    return await safePush({ name: 'Training' })
  }

  /**
   * 导航到报告页面
   */
  async function navigateToReport(reportData = null) {
    if (!canNavigateToReport.value) {
      console.warn('当前状态不允许查看报告')
      console.error('导航失败: 当前状态不允许查看报告')
      return false
    }

    const query = reportData ? { reportId: reportData.id } : {}
    return await safePush({ name: 'Report', query })
  }

  /**
   * 导航到错误页面
   */
  async function navigateToError(errorType = 'unknown', details = null) {
    const query = { error: errorType }
    if (details) {
      query.details = details
    }
    
    await safePush({ name: 'Error', query })
  }

  /**
   * 带确认的导航
   */
  async function navigateWithConfirm(to, message = '确定要离开当前页面吗？') {
    try {
      await ElMessageBox.confirm(message, '确认导航', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      
      return await safePush(to)
      
    } catch (error) {
      if (error === 'cancel') {
        console.log('用户取消导航')
        return false
      }
      throw error
    }
  }

  /**
   * 刷新当前页面
   */
  function refreshCurrentPage() {
    router.go(0)
  }

  /**
   * 获取路由历史信息
   */
  function getRouteHistory() {
    return {
      current: {
        name: currentRouteName.value,
        path: currentPath.value,
        query: routeQuery.value,
        params: routeParams.value
      },
      canGoBack: window.history.length > 1
    }
  }

  /**
   * 检查路由权限
   */
  function checkRoutePermission(routeName) {
    const routePermissions = {
      'Login': true,
      'Training': canNavigateToTraining.value,
      'Report': canNavigateToReport.value,
      'Error': true
    }
    
    return routePermissions[routeName] !== false
  }

  /**
   * 生成带参数的路由URL
   */
  function generateRouteUrl(routeName, params = {}, query = {}) {
    try {
      return router.resolve({
        name: routeName,
        params,
        query
      }).href
    } catch (error) {
      console.error('生成路由URL失败:', error)
      return '#'
    }
  }

  return {
    // 路由信息
    currentRouteName,
    currentPath,
    routeQuery,
    routeParams,
    
    // 导航状态
    canNavigateToTraining,
    canNavigateToReport,
    
    // 导航方法
    safePush,
    safeReplace,
    safeBack,
    navigateBySystemState,
    navigateToLogin,
    navigateToTraining,
    navigateToReport,
    navigateToError,
    navigateWithConfirm,
    refreshCurrentPage,
    
    // 工具方法
    getRouteHistory,
    checkRoutePermission,
    generateRouteUrl
  }
}

/**
 * 页面过渡动画组合式API
 * @returns {object} 页面过渡相关的配置
 */
export function usePageTransition() {
  const route = useRoute()
  
  const transitionName = computed(() => {
    // 根据路由名称返回不同的过渡动画
    const transitionMap = {
      'Login': 'fade',
      'Training': 'slide-left',
      'Report': 'slide-up',
      'Error': 'fade'
    }
    
    return transitionMap[route.name] || 'fade'
  })
  
  const transitionMode = computed(() => 'out-in')
  
  return {
    transitionName,
    transitionMode
  }
}

/**
 * 面包屑导航组合式API
 * @returns {object} 面包屑相关的数据和方法
 */
export function useBreadcrumb() {
  const route = useRoute()
  const mainStore = useMainStore()

  const breadcrumbItems = computed(() => {
    const items = []
    const userDisplayName = mainStore.userInfo?.name || mainStore.userInfo?.patient_id || '用户'

    // 根据当前路由生成面包屑
    switch (route.name) {
      case 'Login':
        items.push({ title: '首页', path: '/' })
        items.push({ title: '用户登录' })
        break

      case 'Training':
        items.push({ title: '首页', path: '/' })
        items.push({ title: '康复训练' })
        if (mainStore.isUserLoggedIn) {
          items.push({ title: userDisplayName })
        }
        break

      case 'Report':
        items.push({ title: '首页', path: '/' })
        items.push({ title: '康复训练', path: '/training' })
        items.push({ title: '训练报告' })
        break

      case 'Error':
        items.push({ title: '首页', path: '/' })
        items.push({ title: '系统错误' })
        break
    }

    return items
  })

  return {
    breadcrumbItems
  }
}
