/**
 * WebSocket Composable
 * 为Vue组件提供WebSocket功能的组合式API
 */

import { ref, reactive, onMounted, onUnmounted } from 'vue'
import websocketService from '@/services/websocket'

/**
 * WebSocket组合式API
 * @returns {object} WebSocket相关的响应式数据和方法
 */
export function useWebSocket() {
  // 响应式状态
  const isConnected = ref(false)
  const isConnecting = ref(false)
  const connectionError = ref(null)
  const lastMessage = ref(null)
  const reconnectAttempts = ref(0)
  
  // 连接状态统计
  const stats = reactive({
    totalMessages: 0,
    messagesByType: {},
    connectionTime: null,
    lastHeartbeat: null
  })

  // 事件订阅管理
  const subscriptions = new Map()

  /**
   * 连接WebSocket
   */
  const connect = async () => {
    try {
      isConnecting.value = true
      connectionError.value = null
      
      await websocketService.connect()
      
      isConnected.value = true
      stats.connectionTime = new Date()
      
      console.log('Socket.IO连接成功')
    } catch (error) {
      connectionError.value = error.message || '连接失败'
      console.error('WebSocket连接失败:', error)
    } finally {
      isConnecting.value = false
    }
  }

  /**
   * 断开WebSocket连接
   */
  const disconnect = () => {
    websocketService.disconnect()
    isConnected.value = false
    isConnecting.value = false
    stats.connectionTime = null
    
    // 清理所有订阅
    subscriptions.clear()
  }

  /**
   * 订阅WebSocket事件
   * @param {string} eventType - 事件类型
   * @param {function} callback - 回调函数
   * @returns {function} 取消订阅函数
   */
  const subscribe = (eventType, callback) => {
    // 包装回调函数以更新统计信息
    const wrappedCallback = (data) => {
      lastMessage.value = {
        type: eventType,
        data,
        timestamp: Date.now()
      }
      stats.totalMessages++
      stats.messagesByType[eventType] = (stats.messagesByType[eventType] || 0) + 1
      
      // 调用原始回调
      callback(data)
    }

    const unsubscribe = websocketService.subscribe(eventType, wrappedCallback)
    
    // 存储订阅信息
    if (!subscriptions.has(eventType)) {
      subscriptions.set(eventType, new Set())
    }
    subscriptions.get(eventType).add(unsubscribe)
    
    return unsubscribe
  }

  /**
   * 发送消息
   * @param {string} eventType - 事件类型
   * @param {object} data - 消息数据
   */
  const send = (eventType, data = {}) => {
    websocketService.send(eventType, data)
  }

  /**
   * 获取连接状态
   */
  const getStatus = () => {
    const serviceStatus = websocketService.getConnectionStatus()
    
    return {
      isConnected: isConnected.value,
      isConnecting: isConnecting.value,
      error: connectionError.value,
      reconnectAttempts: serviceStatus.reconnectAttempts,
      queuedMessages: serviceStatus.queuedMessages,
      stats: { ...stats }
    }
  }

  /**
   * 重置统计信息
   */
  const resetStats = () => {
    stats.totalMessages = 0
    stats.messagesByType = {}
    stats.lastHeartbeat = null
  }

  // 监听WebSocket服务的内部事件
  const setupInternalListeners = () => {
    // 监听连接状态变化
    websocketService.subscribe('connected', () => {
      isConnected.value = true
      isConnecting.value = false
      connectionError.value = null
      stats.connectionTime = new Date()
    })

    websocketService.subscribe('disconnected', () => {
      isConnected.value = false
      isConnecting.value = false
      stats.connectionTime = null
    })

    websocketService.subscribe('error', (data) => {
      connectionError.value = data.error?.message || '连接错误'
      isConnecting.value = false
    })

    websocketService.subscribe('reconnect_failed', (data) => {
      reconnectAttempts.value = data.attempts
      connectionError.value = '重连失败，请检查网络连接'
    })
  }

  // 组件挂载时设置监听器
  onMounted(() => {
    setupInternalListeners()
  })

  // 组件卸载时清理资源
  onUnmounted(() => {
    // 清理所有订阅
    subscriptions.forEach(unsubscribeSet => {
      unsubscribeSet.forEach(unsubscribe => unsubscribe())
    })
    subscriptions.clear()
  })

  return {
    // 响应式状态
    isConnected,
    isConnecting,
    connectionError,
    lastMessage,
    reconnectAttempts,
    stats,
    
    // 方法
    connect,
    disconnect,
    subscribe,
    send,
    getStatus,
    resetStats
  }
}

/**
 * 简化的WebSocket钩子，自动连接
 * @param {boolean} autoConnect - 是否自动连接
 * @returns {object} WebSocket相关功能
 */
export function useAutoWebSocket(autoConnect = true) {
  const ws = useWebSocket()
  
  onMounted(async () => {
    if (autoConnect) {
      await ws.connect()
    }
  })
  
  return ws
}

/**
 * 特定消息类型的WebSocket钩子
 * @param {string} messageType - 消息类型
 * @param {function} callback - 回调函数
 * @param {boolean} autoConnect - 是否自动连接
 * @returns {object} WebSocket相关功能
 */
export function useWebSocketMessage(messageType, callback, autoConnect = true) {
  const ws = useWebSocket()
  
  onMounted(async () => {
    if (autoConnect) {
      await ws.connect()
    }
    
    // 订阅特定消息类型
    ws.subscribe(messageType, callback)
  })
  
  return ws
}
