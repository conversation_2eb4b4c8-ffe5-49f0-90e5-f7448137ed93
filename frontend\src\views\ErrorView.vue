<template>
  <div class="error-view h-screen flex-center bg-gradient-to-br from-red-50 to-orange-100">
    <div class="text-center max-w-md mx-auto p-6">
      <!-- 错误图标 -->
      <div class="mb-6">
        <el-icon :size="80" class="text-red-500">
          <WarningFilled />
        </el-icon>
      </div>
      
      <!-- 错误标题 -->
      <h1 class="text-3xl font-bold text-gray-800 mb-4">
        {{ errorTitle }}
      </h1>
      
      <!-- 错误描述 -->
      <p class="text-gray-600 mb-6 leading-relaxed">
        {{ errorDescription }}
      </p>
      
      <!-- 错误详情（开发模式） -->
      <div v-if="isDev && errorDetails" class="mb-6">
        <el-collapse>
          <el-collapse-item title="错误详情" name="details">
            <pre class="text-left text-sm bg-gray-100 p-3 rounded overflow-auto">{{ errorDetails }}</pre>
          </el-collapse-item>
        </el-collapse>
      </div>
      
      <!-- 系统状态信息 -->
      <div class="mb-6">
        <el-card class="text-left">
          <template #header>
            <span class="text-sm font-medium">系统状态</span>
          </template>
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span>连接状态:</span>
              <el-tag :type="connectionStatus.type" size="small">
                {{ connectionStatus.text }}
              </el-tag>
            </div>
            <div class="flex justify-between">
              <span>系统状态:</span>
              <span class="font-medium">{{ systemState }}</span>
            </div>
            <div class="flex justify-between">
              <span>错误时间:</span>
              <span>{{ errorTime }}</span>
            </div>
          </div>
        </el-card>
      </div>
      
      <!-- 操作按钮 -->
      <div class="space-y-3">
        <el-button 
          type="primary" 
          size="large" 
          class="w-full"
          @click="handleRetry"
          :loading="retrying"
        >
          <el-icon class="mr-2"><Refresh /></el-icon>
          重试连接
        </el-button>
        
        <el-button 
          size="large" 
          class="w-full"
          @click="handleBackToHome"
        >
          <el-icon class="mr-2"><HomeFilled /></el-icon>
          返回首页
        </el-button>
        
        <el-button 
          size="large" 
          class="w-full"
          @click="handleReload"
        >
          <el-icon class="mr-2"><RefreshRight /></el-icon>
          刷新页面
        </el-button>
      </div>
      
      <!-- 帮助信息 -->
      <div class="mt-8 text-sm text-gray-500">
        <p>如果问题持续存在，请联系技术支持</p>
        <p class="mt-1">错误代码: {{ errorCode }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMainStore } from '@/stores/main'
import websocketService from '@/services/websocket'
import { WarningFilled, Refresh, HomeFilled, RefreshRight } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const mainStore = useMainStore()

// 系统状态
const isConnected = computed(() => mainStore.isConnected)
const connStatus = computed(() => mainStore.isConnected ? 'connected' : 'disconnected')
const currentState = computed(() => mainStore.currentState)

// WebSocket连接方法
const connectWebSocket = async () => {
  try {
    await websocketService.connect()
  } catch (error) {
    console.error('WebSocket连接失败:', error)
  }
}

// 响应式数据
const retrying = ref(false)
const errorTime = ref(new Date().toLocaleString())
const isDev = ref(import.meta.env.DEV)

// 计算属性
const errorType = computed(() => route.query.error || 'unknown')

const errorTitle = computed(() => {
  const errorTitles = {
    'connection_lost': '连接中断',
    'navigation_failed': '页面导航失败',
    'websocket_error': 'WebSocket连接错误',
    'authentication_failed': '认证失败',
    'system_error': '系统错误',
    'unknown': '未知错误'
  }
  
  return errorTitles[errorType.value] || '系统错误'
})

const errorDescription = computed(() => {
  const errorDescriptions = {
    'connection_lost': '与服务器的连接已中断，请检查网络连接或稍后重试。',
    'navigation_failed': '页面导航过程中发生错误，请重试或返回首页。',
    'websocket_error': 'WebSocket连接发生错误，实时功能可能受到影响。',
    'authentication_failed': '用户认证失败，请重新登录。',
    'system_error': '系统发生内部错误，请稍后重试。',
    'unknown': '发生了未知错误，请刷新页面或联系技术支持。'
  }
  
  return errorDescriptions[errorType.value] || '系统遇到了一个问题，正在尝试恢复。'
})

const errorDetails = computed(() => {
  return route.query.details || null
})

const errorCode = computed(() => {
  return `ERR_${errorType.value.toUpperCase()}_${Date.now().toString().slice(-6)}`
})

const connectionStatus = computed(() => {
  if (isConnected.value) {
    return { type: 'success', text: '已连接' }
  } else {
    return { type: 'danger', text: '未连接' }
  }
})

const systemState = computed(() => currentState.value || '未知')

// 方法
const handleRetry = async () => {
  retrying.value = true
  
  try {
    console.log('尝试重新连接...')
    
    // 尝试重新连接WebSocket
    await connectWebSocket()
    
    // 连接成功后返回首页
    if (isConnected.value) {
      ElMessage.success('连接恢复成功')
      await router.push({ name: 'Login' })
    } else {
      ElMessage.error('连接恢复失败，请稍后重试')
    }
    
  } catch (error) {
    console.error('重试连接失败:', error)
    ElMessage.error('重试失败: ' + error.message)
  } finally {
    retrying.value = false
  }
}

const handleBackToHome = async () => {
  try {
    await router.push({ name: 'Login' })
  } catch (error) {
    console.error('返回首页失败:', error)
    handleReload()
  }
}

const handleReload = () => {
  window.location.reload()
}

// 生命周期
onMounted(() => {
  console.log('错误页面加载:', {
    errorType: errorType.value,
    query: route.query,
    systemState: systemState.value,
    connected: isConnected.value
  })
  
  // 记录错误信息
  if (isDev.value) {
    console.error('错误页面详情:', {
      type: errorType.value,
      title: errorTitle.value,
      description: errorDescription.value,
      details: errorDetails.value,
      code: errorCode.value,
      timestamp: errorTime.value
    })
  }
})
</script>

<style scoped>
.error-view {
  background-image: 
    radial-gradient(at 40% 20%, rgb(239, 68, 68, 0.1) 0px, transparent 50%),
    radial-gradient(at 80% 0%, rgb(239, 68, 68, 0.1) 0px, transparent 50%),
    radial-gradient(at 0% 50%, rgb(239, 68, 68, 0.1) 0px, transparent 50%);
}

.el-collapse {
  border: none;
}

.el-collapse :deep(.el-collapse-item__header) {
  background-color: transparent;
  border: none;
  font-size: 14px;
}

.el-collapse :deep(.el-collapse-item__content) {
  padding-bottom: 0;
}

pre {
  max-height: 200px;
  font-family: 'Courier New', monospace;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
