<template>
  <div class="h-screen overflow-hidden relative bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/20 via-purple-500/20 to-pink-500/20 pointer-events-none"></div>

    <!-- 主要内容区域 -->
    <div class="relative z-10 h-full flex flex-col items-center justify-center p-4">
     
      <!-- 主要内容卡片 - 放大容器 -->
      <div class="max-w-7xl w-full backdrop-blur-lg bg-white/10 rounded-3xl p-6 border border-white/20 shadow-2xl">
         <!-- 系统标题 -->
      <div class="text-center mb-6">
        <h1 class="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-white to-indigo-100 bg-clip-text text-transparent mb-3 drop-shadow-sm">
          智能康复系统
        </h1>
        <p class="text-lg text-white/80 font-light mb-3">
          Intelligent Rehabilitation System
        </p>
        <div class="flex items-center justify-center space-x-2">
          <div :class="[
            'w-2 h-2 rounded-full animate-pulse',
            systemStatusClass === 'status-success' ? 'bg-green-400 shadow-lg shadow-green-400/50' : '',
            systemStatusClass === 'status-warning' ? 'bg-yellow-400 shadow-lg shadow-yellow-400/50' : '',
            systemStatusClass === 'status-error' ? 'bg-red-400 shadow-lg shadow-red-400/50' : '',
            systemStatusClass === 'status-info' ? 'bg-blue-400 shadow-lg shadow-blue-400/50' : ''
          ]"></div>
          <span class="text-sm text-white/60">{{ systemStatusText }}</span>
        </div>
      </div>

        <!-- 主要内容卡片 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- 视频显示区域 -->
          <div class="h-full">
            <div class="bg-white/95 backdrop-blur-lg rounded-2xl border border-white/20 shadow-xl overflow-hidden h-full flex flex-col">
              <div class="flex items-center justify-between p-4 border-b border-gray-100 flex-shrink-0">
                <span class="font-semibold text-gray-700">实时画面</span>
                <div class="flex items-center space-x-2">
                  <el-tag :type="videoStatusType" size="small" class="rounded-full">
                    {{ videoStatusText }}
                  </el-tag>
                </div>
              </div>

              <div class="p-4 flex-1 flex flex-col">
                <!-- 视频流组件 - 占据主要空间 -->
                <div class="flex-1 mb-4">
                  <VideoStream
                    ref="videoStreamRef"
                    :width="'100%'"
                    :height="'100%'"
                    :show-controls="false"
                    @stream-ready="handleStreamReady"
                    @stream-error="handleStreamError"
                    class="w-full h-full rounded-xl overflow-hidden"
                  />
                </div>

                <!-- 视频信息 -->
                <div class="text-sm text-gray-500 text-center p-3 bg-gray-50 rounded-xl border border-gray-100 flex-shrink-0">
                  <p>请站在摄像头前，系统将自动检测您的身份</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 检测状态区域 -->
          <div class="space-y-4">
            <div class="bg-white/95 backdrop-blur-lg rounded-2xl border border-white/20 shadow-xl overflow-hidden">
              <div class="p-5 border-b border-gray-100">
                <span class="font-semibold text-gray-700">用户检测</span>
              </div>

              <div class="p-5 space-y-6">
                <!-- 检测状态组件 -->
                <DetectionStatus
                  ref="detectionStatusRef"
                  :detection-count="detectionCount"
                  :last-detection="lastDetection"
                  :is-detecting="isDetecting"
                  @detection-result="handleDetectionResult"
                />

                <!-- 系统状态组件 -->
                <SystemStatus
                  ref="systemStatusRef"
                  :connection-status="connectionStatus"
                  :system-health="systemHealth"
                  :error-count="errorCount"
                />

                <!-- 操作按钮 -->
                <!-- <div class="space-y-3">
                  <el-button
                    type="primary"
                    size="large"
                    class="w-full rounded-xl font-medium transition-all duration-300 hover:-translate-y-0.5 hover:shadow-lg"
                    :loading="isProcessing"
                    :disabled="!canStartDetection"
                    @click="startDetection"
                  >
                    <el-icon class="mr-2">
                      <component :is="detectionButtonIcon" />
                    </el-icon>
                    {{ detectionButtonText }}
                  </el-button>

                  <el-button
                    size="large"
                    class="w-full rounded-xl font-medium transition-all duration-300 hover:-translate-y-0.5 hover:shadow-lg"
                    @click="refreshSystem"
                    :disabled="isProcessing"
                  >
                    <el-icon class="mr-2"><Refresh /></el-icon>
                    刷新系统
                  </el-button>
                </div> -->
              </div>
            </div>
          </div>
        </div>

        <!-- 底部信息 -->
        <div class="text-center mt-8 text-sm text-white/60">
          <p>系统版本 v1.0.0 | 运行时间: {{ systemUptime }}</p>
        </div>
      </div>
    </div>

    <!-- 登录成功过渡动画 -->
    <transition
      enter-active-class="transition-all duration-500 ease-out"
      leave-active-class="transition-all duration-300 ease-in"
      enter-from-class="opacity-0 scale-75"
      enter-to-class="opacity-100 scale-100"
      leave-from-class="opacity-100 scale-100"
      leave-to-class="opacity-0 scale-110"
      appear
    >
      <div v-if="showLoginSuccess" class="fixed inset-0 bg-gradient-to-br from-green-500/95 to-emerald-600/95 backdrop-blur-lg z-50 flex items-center justify-center">
        <div class="text-center max-w-md p-8">
          <div class="mb-6 animate-bounce">
            <el-icon :size="80" class="text-white drop-shadow-lg">
              <CircleCheckFilled />
            </el-icon>
          </div>
          <h2 class="text-3xl font-bold text-white mb-4 drop-shadow-sm">
            登录成功！
          </h2>
          <p class="text-lg text-white/90 mb-6">
            欢迎 {{ userDisplayName }}，正在进入训练系统...
          </p>
          <div class="w-full">
            <el-progress
              :percentage="loginProgress"
              :show-text="false"
              stroke-width="4"
              color="#ffffff"
              class="mb-2"
            />
            <p class="text-sm text-white/80">{{ loginProgress }}%</p>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useMainStore } from '@/stores/main'
import { useNavigation } from '@/utils/useRouter'
import websocketService from '@/services/websocket'
import VideoStream from '@/components/common/VideoStream.vue'
import DetectionStatus from '@/components/common/DetectionStatus.vue'
import SystemStatus from '@/components/common/SystemStatus.vue'
import {
  CircleCheckFilled,
  VideoCamera,
  VideoPause,
  Refresh,
  Loading
} from '@element-plus/icons-vue'

// 路由和导航
const { navigateToTraining } = useNavigation()

// 状态管理 (使用主store)
const mainStore = useMainStore()

// 系统状态
const currentState = computed(() => mainStore.currentState)
const isConnected = computed(() => mainStore.isConnected)
const connectionStatus = computed(() => mainStore.isConnected ? 'connected' : 'disconnected')

// 用户状态
const isAuthenticated = computed(() => mainStore.isUserLoggedIn)
const userDisplayName = computed(() => mainStore.userInfo?.name || mainStore.userInfo?.patient_id || '用户')

// 系统健康状态（简化实现）
const systemHealth = computed(() => {
  if (isConnected.value) return 100
  return 0
})

// 模板引用
const videoStreamRef = ref(null)
const detectionStatusRef = ref(null)
const systemStatusRef = ref(null)

// 组件状态
const isProcessing = ref(false)
const isDetecting = ref(false)
const showLoginSuccess = ref(false)
const loginProgress = ref(0)
const detectionCount = ref(0)
const lastDetection = ref(null)
const systemStartTime = ref(new Date())

// 视频状态
const videoStatus = ref('disconnected') // disconnected, connecting, connected, error

// 计算属性
const systemStatusClass = computed(() => {
  if (!isConnected.value) return 'status-error'
  if (currentState.value === 'WAITING') return 'status-success'
  if (currentState.value === 'USER_LOGIN') return 'status-warning'
  return 'status-info'
})

const systemStatusText = computed(() => {
  if (!isConnected.value) return '系统未连接'

  const stateTextMap = {
    'IDLE': '系统空闲',
    'WAITING': '等待用户',
    'USER_LOGIN': '用户登录中',
    'ACTION_PREPARATION': '准备训练',
    'ACTION_TRAINING': '训练中',
    'ACTION_REST': '休息中',
    'ACTION_COMPLETED': '训练完成'
  }
  console.log(currentState.value)
  return stateTextMap[currentState.value] || '系统状态未知'
})

const videoStatusType = computed(() => {
  const statusTypeMap = {
    'disconnected': 'danger',
    'connecting': 'warning',
    'connected': 'success',
    'error': 'danger'
  }
  return statusTypeMap[videoStatus.value] || 'info'
})

const videoStatusText = computed(() => {
  const statusTextMap = {
    'disconnected': '未连接',
    'connecting': '连接中',
    'connected': '已连接',
    'error': '连接错误'
  }
  return statusTextMap[videoStatus.value] || '未知状态'
})

const canStartDetection = computed(() => {
  return isConnected.value &&
         videoStatus.value === 'connected' &&
         !isProcessing.value &&
         currentState.value === 'WAITING'
})


// 简化错误计数（主store中没有errorMessages，使用简化实现）
const errorCount = computed(() => {
  // 基于连接状态简单计算错误数
  return isConnected.value ? 0 : 1
})

const systemUptime = computed(() => {
  const now = new Date()
  const diff = now.getTime() - systemStartTime.value.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(minutes / 60)

  if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`
  }
  return `${minutes}分钟`
})

// 监听用户认证状态变化
watch(isAuthenticated, (authenticated) => {
  if (authenticated) {
    handleLoginSuccess()
  }
})

// 监听系统状态变化
watch(currentState, (newState) => {
  if (newState === 'USER_LOGIN') {
    isDetecting.value = true
  } else if (newState === 'WAITING') {
    isDetecting.value = false
    isProcessing.value = false
  }
})

// 生命周期
onMounted(() => {
  console.log('登录页面加载完成')
  initializePage()
})

onUnmounted(() => {
  cleanup()
})

// 方法
const initializePage = async () => {
  try {
    // 重置状态
    resetPageState()
    // 检查并建立WebSocket连接
    if (!isConnected.value) {
      console.log('WebSocket未连接，尝试建立连接...')
      try {
        await websocketService.connect()
        ElMessage.success('系统连接成功')
      } catch (error) {
        console.error('WebSocket连接失败:', error)
        ElMessage.warning('系统连接失败，部分功能可能不可用')
        // 继续加载页面，但显示连接状态
      }
    }
    // 如果用户已登录，直接跳转
    if (isAuthenticated.value) {
      await navigateToTraining()
      return
    }
    console.log('登录页面初始化完成')

  } catch (error) {
    console.error('登录页面初始化失败:', error)
    ElMessage.error('页面初始化失败: ' + error.message)
  }
}

const resetPageState = () => {
  isProcessing.value = false
  isDetecting.value = false
  showLoginSuccess.value = false
  loginProgress.value = 0
  detectionCount.value = 0
  lastDetection.value = null
}
const handleStreamReady = () => {
  videoStatus.value = 'connected'
  console.log('视频流准备就绪')
}

const handleStreamError = (error) => {
  videoStatus.value = 'error'
  console.error('视频流错误:', error)
  ElMessage.error('视频流连接失败')
}

const handleDetectionResult = (result) => {
  detectionCount.value++
  lastDetection.value = {
    timestamp: new Date(),
    result: result.success,
    confidence: result.confidence || 0,
    patientId: result.patientId
  }

  if (result.success) {
    console.log('检测到用户:', result.patientId)
  }
}

const handleLoginSuccess = async () => {
  try {
    showLoginSuccess.value = true
    isProcessing.value = true

    // 登录进度动画
    const progressInterval = setInterval(() => {
      loginProgress.value += 10
      if (loginProgress.value >= 100) {
        clearInterval(progressInterval)
      }
    }, 100)

    // 延迟3秒后跳转到训练页面
    setTimeout(async () => {
      try {
        await navigateToTraining()
      } catch (error) {
        console.error('跳转训练页面失败:', error)
        ElMessage.error('页面跳转失败')
        showLoginSuccess.value = false
        isProcessing.value = false
      }
    }, 3000)

  } catch (error) {
    console.error('登录成功处理失败:', error)
    showLoginSuccess.value = false
    isProcessing.value = false
  }
}

const refreshSystem = async () => {
  try {
    isProcessing.value = true

    // 重新初始化页面
    await initializePage()

    ElMessage.success('系统刷新完成')

  } catch (error) {
    console.error('系统刷新失败:', error)
    ElMessage.error('系统刷新失败: ' + error.message)
  } finally {
    isProcessing.value = false
  }
}

const cleanup = () => {
  // 清理定时器和事件监听器
  resetPageState()
}
</script>


