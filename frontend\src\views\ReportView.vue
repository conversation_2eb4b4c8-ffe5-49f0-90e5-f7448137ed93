<template>
  <div class="report-view min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 overflow-auto">
    <!-- 顶部导航栏 -->
    <div class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-10">
      <div class="container mx-auto px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <h1 class="text-2xl font-bold text-gray-800">训练报告</h1>
            <div class="text-sm text-gray-500">
              {{ formatDate(trainingSession?.start_time) }}
            </div>
          </div>

          <div class="flex items-center space-x-3">
            <el-button
              type="primary"
              @click="handleNewTraining"
              :loading="isNavigating"
            >
              开始新训练
            </el-button>
            <el-button
              @click="handleBackToHome"
              :loading="isNavigating"
            >
              返回首页
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <div class="container mx-auto p-6">
      <!-- 加载状态 -->
      <div v-if="!trainingSession" class="text-center py-20">
        <el-icon size="48" class="text-gray-400 mb-4"><Loading /></el-icon>
        <div class="text-lg text-gray-600">正在加载训练报告...</div>
      </div>

      <!-- 报告内容 -->
      <div v-else class="space-y-6">
        <!-- 总体成绩卡片 -->
        <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
          <div class="bg-gradient-to-r from-green-500 to-green-600 text-white p-6">
            <div class="flex items-center justify-between">
              <div>
                <h2 class="text-2xl font-bold mb-2">训练完成！</h2>
                <p class="text-green-100">恭喜您完成了本次康复训练</p>
              </div>
              <div class="text-right">
                <div class="text-4xl font-bold mb-1">{{ overallScore }}</div>
                <div class="text-sm text-green-100">总体评分</div>
              </div>
            </div>
          </div>

          <div class="p-6">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div class="text-center">
                <div class="text-2xl font-bold text-blue-600 mb-1">{{ formatDuration(trainingSession.duration_seconds) }}</div>
                <div class="text-sm text-gray-600">训练时长</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-purple-600 mb-1">{{ trainingSession.total_actions_completed }}</div>
                <div class="text-sm text-gray-600">完成动作</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-orange-600 mb-1">{{ Math.round(trainingSession.completion_rate * 100) }}%</div>
                <div class="text-sm text-gray-600">完成率</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-red-600 mb-1">{{ getScoreLevel(overallScore) }}</div>
                <div class="text-sm text-gray-600">评价等级</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 详细报告网格 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- 动作详情 -->
          <div class="bg-white rounded-xl shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4">
              <h3 class="text-lg font-semibold">动作完成详情</h3>
            </div>

            <div class="p-4 max-h-96 overflow-y-auto">
              <div class="space-y-3">
                <div
                  v-for="(action, index) in completedActions"
                  :key="index"
                  class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
                >
                  <div class="flex items-center justify-between mb-2">
                    <div class="font-medium text-gray-800">
                      {{ getActionDisplayName(action.action_type) }}
                    </div>
                    <div :class="[
                      'px-2 py-1 rounded text-sm font-medium',
                      getScoreColorClass(action.average_score)
                    ]">
                      {{ Math.round(action.average_score) }}分
                    </div>
                  </div>

                  <div class="grid grid-cols-3 gap-2 text-sm text-gray-600">
                    <div>组数: {{ action.sets_completed }}/{{ action.total_sets }}</div>
                    <div>次数: {{ action.reps_completed }}</div>
                    <div>用时: {{ formatDuration(action.duration_seconds) }}</div>
                  </div>

                  <div class="mt-2">
                    <el-progress
                      :percentage="Math.round((action.sets_completed / action.total_sets) * 100)"
                      :stroke-width="6"
                      :color="getProgressColor(action.average_score)"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 成绩分析图表 -->
          <div class="bg-white rounded-xl shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-4">
              <h3 class="text-lg font-semibold">成绩分析</h3>
            </div>

            <div class="p-4">
              <div class="h-64 relative">
                <!-- 简单的柱状图 -->
                <div class="flex items-end justify-around h-full">
                  <div
                    v-for="(action, index) in completedActions"
                    :key="index"
                    class="flex flex-col items-center"
                  >
                    <div
                      :style="{ height: `${(action.average_score / 100) * 200}px` }"
                      :class="[
                        'w-12 rounded-t transition-all duration-500',
                        getBarColor(action.average_score)
                      ]"
                    ></div>
                    <div class="text-xs text-gray-600 mt-2 text-center">
                      {{ getActionShortName(action.action_type) }}
                    </div>
                    <div class="text-xs font-medium text-gray-800">
                      {{ Math.round(action.average_score) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 训练建议和反馈 -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden">
          <div class="bg-gradient-to-r from-orange-500 to-orange-600 text-white p-4">
            <h3 class="text-lg font-semibold">训练建议与反馈</h3>
          </div>

          <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- 优点 -->
              <div>
                <h4 class="text-lg font-medium text-green-700 mb-3 flex items-center">
                  <el-icon class="mr-2"><CircleCheckFilled /></el-icon>
                  做得好的地方
                </h4>
                <ul class="space-y-2">
                  <li v-for="strength in strengths" :key="strength" class="flex items-start">
                    <span class="text-green-500 mr-2 mt-1">✓</span>
                    <span class="text-gray-700">{{ strength }}</span>
                  </li>
                </ul>
              </div>

              <!-- 改进建议 -->
              <div>
                <h4 class="text-lg font-medium text-orange-700 mb-3 flex items-center">
                  <el-icon class="mr-2"><Warning /></el-icon>
                  改进建议
                </h4>
                <ul class="space-y-2">
                  <li v-for="suggestion in suggestions" :key="suggestion" class="flex items-start">
                    <span class="text-orange-500 mr-2 mt-1">!</span>
                    <span class="text-gray-700">{{ suggestion }}</span>
                  </li>
                </ul>
              </div>
            </div>

            <!-- 总体评价 -->
            <div class="mt-6 p-4 bg-blue-50 rounded-lg">
              <h4 class="text-lg font-medium text-blue-700 mb-2">总体评价</h4>
              <p class="text-gray-700">{{ overallFeedback }}</p>
            </div>
          </div>
        </div>

        <!-- 历史对比（如果有历史数据） -->
        <div v-if="showHistoryComparison" class="bg-white rounded-xl shadow-lg overflow-hidden">
          <div class="bg-gradient-to-r from-indigo-500 to-indigo-600 text-white p-4">
            <h3 class="text-lg font-semibold">进步对比</h3>
          </div>

          <div class="p-6">
            <div class="text-center text-gray-500">
              <el-icon size="48" class="mb-2"><TrendCharts /></el-icon>
              <div>历史数据对比功能即将推出</div>
            </div>
          </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="bg-white rounded-xl shadow-lg p-6">
          <div class="text-center space-y-4">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">接下来要做什么？</h3>

            <div class="flex flex-col sm:flex-row gap-4 justify-center">
              <el-button
                type="primary"
                size="large"
                @click="handleNewTraining"
                :loading="isNavigating"
                class="px-8"
              >
                <el-icon class="mr-2"><Refresh /></el-icon>
                开始新训练
              </el-button>

              <el-button
                size="large"
                @click="handleViewHistory"
                :loading="isNavigating"
                class="px-8"
              >
                <el-icon class="mr-2"><Document /></el-icon>
                查看历史记录
              </el-button>

              <el-button
                size="large"
                @click="handleBackToHome"
                :loading="isNavigating"
                class="px-8"
              >
                <el-icon class="mr-2"><House /></el-icon>
                返回首页
              </el-button>
            </div>

            <!-- 分享功能 -->
            <div class="mt-6 pt-4 border-t border-gray-200">
              <div class="text-sm text-gray-600 mb-3">分享您的训练成果</div>
              <div class="flex justify-center space-x-3">
                <el-button
                  size="small"
                  @click="handleExportReport"
                  :loading="isExporting"
                >
                  <el-icon class="mr-1"><Download /></el-icon>
                  导出报告
                </el-button>

                <el-button
                  size="small"
                  @click="handlePrintReport"
                >
                  <el-icon class="mr-1"><Printer /></el-icon>
                  打印报告
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useMainStore } from '@/stores/main'
import {
  Loading,
  CircleCheckFilled,
  Warning,
  TrendCharts,
  Refresh,
  Document,
  House,
  Download,
  Printer
} from '@element-plus/icons-vue'

// 路由和store
const router = useRouter()
const mainStore = useMainStore()

// 响应式数据
const isNavigating = ref(false)
const isExporting = ref(false)
const showHistoryComparison = ref(false) // 暂时关闭历史对比功能

// 计算属性
const trainingSession = computed(() => {
  return mainStore.trainingSession
})

const overallScore = computed(() => {
  return Math.round(trainingSession.value?.average_score || 0)
})

const completedActions = computed(() => {
  if (!trainingSession.value?.completed_actions) return []

  return trainingSession.value.completed_actions.map(action => ({
    action_type: action.action_type || action.action_info?.action_type,
    average_score: action.average_score || 0,
    sets_completed: action.sets_completed || 0,
    total_sets: action.total_sets || action.action_info?.sets || 0,
    reps_completed: action.reps_completed || 0,
    duration_seconds: action.duration_seconds || 0
  }))
})

const strengths = computed(() => {
  const result = []

  if (overallScore.value >= 90) {
    result.push('整体表现优秀，动作标准度很高')
  } else if (overallScore.value >= 80) {
    result.push('动作完成质量良好')
  }

  if (trainingSession.value?.completion_rate >= 0.9) {
    result.push('训练完成度很高，坚持性很好')
  }

  // 分析各个动作的表现
  completedActions.value.forEach(action => {
    if (action.average_score >= 85) {
      result.push(`${getActionDisplayName(action.action_type)}动作掌握良好`)
    }
  })

  if (result.length === 0) {
    result.push('积极参与训练，态度认真')
  }

  return result
})

const suggestions = computed(() => {
  const result = []

  if (overallScore.value < 70) {
    result.push('建议加强基础动作练习，注意动作标准性')
  }

  if (trainingSession.value?.completion_rate < 0.8) {
    result.push('建议增加训练频率，提高完成度')
  }

  // 分析各个动作的改进建议
  completedActions.value.forEach(action => {
    if (action.average_score < 70) {
      result.push(`${getActionDisplayName(action.action_type)}动作需要加强练习`)
    }
  })

  if (result.length === 0) {
    result.push('继续保持当前训练强度，可适当增加难度')
  }

  return result
})

const overallFeedback = computed(() => {
  const score = overallScore.value
  const completionRate = trainingSession.value?.completion_rate || 0

  if (score >= 90 && completionRate >= 0.9) {
    return '恭喜您！本次训练表现非常出色，动作标准，完成度高。请继续保持这种良好的训练状态，相信您的康复效果会越来越好。'
  } else if (score >= 80 && completionRate >= 0.8) {
    return '本次训练表现良好，大部分动作都能正确完成。建议在后续训练中继续提高动作的精准度和稳定性。'
  } else if (score >= 70) {
    return '训练基础较好，但还有提升空间。建议多关注动作细节，适当放慢速度确保动作标准性。'
  } else {
    return '训练态度值得肯定，但动作准确性需要改进。建议在专业指导下加强基础动作练习，循序渐进提高训练质量。'
  }
})

// 工具方法
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatDuration = (seconds) => {
  if (!seconds) return '0分钟'
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)

  if (minutes === 0) {
    return `${remainingSeconds}秒`
  } else if (remainingSeconds === 0) {
    return `${minutes}分钟`
  } else {
    return `${minutes}分${remainingSeconds}秒`
  }
}

const getActionDisplayName = (actionType) => {
  const actionNames = {
    'shoulder_touch': '左手摸右肩',
    'arm_raise': '双臂上举',
    'finger_touch': '对指练习',
    'palm_flip': '手掌翻转'
  }
  return actionNames[actionType] || actionType
}

const getActionShortName = (actionType) => {
  const shortNames = {
    'shoulder_touch': '摸肩',
    'arm_raise': '举臂',
    'finger_touch': '对指',
    'palm_flip': '翻掌'
  }
  return shortNames[actionType] || actionType
}

const getScoreLevel = (score) => {
  if (score >= 90) return '优秀'
  if (score >= 80) return '良好'
  if (score >= 70) return '一般'
  if (score >= 60) return '需改进'
  return '需努力'
}

const getScoreColorClass = (score) => {
  if (score >= 90) return 'bg-green-100 text-green-800'
  if (score >= 80) return 'bg-blue-100 text-blue-800'
  if (score >= 70) return 'bg-yellow-100 text-yellow-800'
  if (score >= 60) return 'bg-orange-100 text-orange-800'
  return 'bg-red-100 text-red-800'
}

const getProgressColor = (score) => {
  if (score >= 90) return '#10b981'
  if (score >= 80) return '#3b82f6'
  if (score >= 70) return '#f59e0b'
  if (score >= 60) return '#f97316'
  return '#ef4444'
}

const getBarColor = (score) => {
  if (score >= 90) return 'bg-green-500'
  if (score >= 80) return 'bg-blue-500'
  if (score >= 70) return 'bg-yellow-500'
  if (score >= 60) return 'bg-orange-500'
  return 'bg-red-500'
}

// 事件处理方法
const handleNewTraining = async () => {
  try {
    const result = await ElMessageBox.confirm(
      '开始新训练将清除当前报告数据，确定要继续吗？',
      '开始新训练',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    if (result === 'confirm') {
      isNavigating.value = true

      // 清除训练数据
      mainStore.resetData()

      // 跳转到登录页面
      setTimeout(() => {
        router.push('/login')
      }, 1000)
    }
  } catch (error) {
    // 用户取消
    console.log('用户取消开始新训练')
  }
}

const handleBackToHome = () => {
  isNavigating.value = true
  router.push('/login')
}

const handleViewHistory = () => {
  ElMessage.info('历史记录功能即将推出')
}

const handleExportReport = async () => {
  isExporting.value = true

  try {
    // 模拟导出过程
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 创建报告数据
    const reportData = {
      session: trainingSession.value,
      generated_at: new Date().toISOString(),
      user: mainStore.userInfo
    }

    // 创建下载链接
    const dataStr = JSON.stringify(reportData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)

    // 下载文件
    const link = document.createElement('a')
    link.href = url
    link.download = `训练报告_${formatDate(trainingSession.value?.start_time).replace(/[:\s]/g, '_')}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    ElMessage.success('报告导出成功')
  } catch (error) {
    console.error('导出报告失败:', error)
    ElMessage.error('导出报告失败')
  } finally {
    isExporting.value = false
  }
}

const handlePrintReport = () => {
  window.print()
}

// 生命周期
onMounted(() => {
  console.log('报告页面加载完成')

  // 检查是否有训练数据
  if (!trainingSession.value) {
    ElMessage.warning('没有找到训练数据，请先完成训练')
    setTimeout(() => {
      router.push('/login')
    }, 2000)
    return
  }

  console.log('训练报告数据:', trainingSession.value)
})
</script>

<style scoped>
.report-view {
  /* 确保固定100vh高度，但允许滚动 */
  height: 100vh;
}
</style>
