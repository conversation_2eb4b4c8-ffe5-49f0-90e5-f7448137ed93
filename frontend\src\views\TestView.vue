<template>
  <div class="min-h-screen bg-gray-100 p-6">
    <div class="max-w-6xl mx-auto space-y-8">
      <!-- 页面标题 -->
      <div class="text-center">
        <h1 class="text-3xl font-bold text-gray-800 mb-2">TailwindCSS 样式测试页面</h1>
        <p class="text-gray-600">验证所有组件的TailwindCSS样式是否正常工作</p>
      </div>

      <!-- 登录页面预览 -->
      <div class="bg-white rounded-xl shadow-lg p-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">登录页面样式预览</h2>
        <div class="h-96 bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 rounded-xl relative overflow-hidden">
          <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/20 via-purple-500/20 to-pink-500/20"></div>
          <div class="relative z-10 h-full flex items-center justify-center">
            <div class="text-center">
              <h3 class="text-4xl font-bold bg-gradient-to-r from-white to-indigo-100 bg-clip-text text-transparent mb-2">
                智能康复系统
              </h3>
              <p class="text-white/80 mb-4">Intelligent Rehabilitation System</p>
              <div class="flex items-center justify-center space-x-2">
                <div class="w-2 h-2 rounded-full bg-green-400 animate-pulse shadow-lg shadow-green-400/50"></div>
                <span class="text-sm text-white/60">系统运行正常</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 组件测试区域 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- VideoStream 组件测试 -->
        <div class="bg-white rounded-xl shadow-lg p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">VideoStream 组件</h3>
          <VideoStream 
            :width="'100%'"
            :height="'240px'"
            :show-controls="true"
            :auto-connect="false"
          />
        </div>

        <!-- DetectionStatus 组件测试 -->
        <div class="bg-white rounded-xl shadow-lg p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">DetectionStatus 组件</h3>
          <DetectionStatus 
            :detection-count="5"
            :last-detection="mockDetection"
            :is-detecting="false"
            :show-history="true"
          />
        </div>

        <!-- SystemStatus 组件测试 -->
        <div class="bg-white rounded-xl shadow-lg p-6 lg:col-span-2">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">SystemStatus 组件</h3>
          <SystemStatus 
            :connection-status="'connected'"
            :system-health="85"
            :error-count="2"
          />
        </div>
      </div>

      <!-- TailwindCSS 工具类测试 -->
      <div class="bg-white rounded-xl shadow-lg p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">TailwindCSS 工具类测试</h3>
        
        <!-- 颜色测试 -->
        <div class="mb-6">
          <h4 class="text-md font-medium text-gray-700 mb-3">颜色系统</h4>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="space-y-2">
              <div class="h-12 bg-green-500 rounded-lg flex items-center justify-center text-white text-sm font-medium">
                Success
              </div>
              <div class="h-8 bg-green-50 rounded border border-green-200 flex items-center justify-center text-green-800 text-xs">
                Success Light
              </div>
            </div>
            <div class="space-y-2">
              <div class="h-12 bg-yellow-500 rounded-lg flex items-center justify-center text-white text-sm font-medium">
                Warning
              </div>
              <div class="h-8 bg-yellow-50 rounded border border-yellow-200 flex items-center justify-center text-yellow-800 text-xs">
                Warning Light
              </div>
            </div>
            <div class="space-y-2">
              <div class="h-12 bg-red-500 rounded-lg flex items-center justify-center text-white text-sm font-medium">
                Error
              </div>
              <div class="h-8 bg-red-50 rounded border border-red-200 flex items-center justify-center text-red-800 text-xs">
                Error Light
              </div>
            </div>
            <div class="space-y-2">
              <div class="h-12 bg-blue-500 rounded-lg flex items-center justify-center text-white text-sm font-medium">
                Primary
              </div>
              <div class="h-8 bg-blue-50 rounded border border-blue-200 flex items-center justify-center text-blue-800 text-xs">
                Primary Light
              </div>
            </div>
          </div>
        </div>

        <!-- 动画测试 -->
        <div class="mb-6">
          <h4 class="text-md font-medium text-gray-700 mb-3">动画效果</h4>
          <div class="flex flex-wrap gap-4">
            <div class="w-12 h-12 bg-blue-500 rounded-full animate-pulse"></div>
            <div class="w-12 h-12 bg-green-500 rounded-full animate-bounce"></div>
            <div class="w-12 h-12 bg-purple-500 rounded-full animate-spin"></div>
            <div class="w-12 h-12 bg-pink-500 rounded-full hover:scale-110 transition-transform duration-300 cursor-pointer"></div>
          </div>
        </div>

        <!-- 渐变测试 -->
        <div class="mb-6">
          <h4 class="text-md font-medium text-gray-700 mb-3">渐变效果</h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="h-24 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center text-white font-medium">
              Linear Gradient
            </div>
            <div class="h-24 bg-gradient-to-br from-green-400 to-blue-500 rounded-lg flex items-center justify-center text-white font-medium">
              Diagonal Gradient
            </div>
            <div class="h-24 bg-gradient-conic-from-green rounded-lg flex items-center justify-center text-white font-medium">
              Conic Gradient
            </div>
          </div>
        </div>

        <!-- 响应式测试 -->
        <div>
          <h4 class="text-md font-medium text-gray-700 mb-3">响应式布局</h4>
          <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            <div class="h-16 bg-gray-200 rounded-lg flex items-center justify-center text-gray-600 text-sm">
              1 Col
            </div>
            <div class="h-16 bg-gray-200 rounded-lg flex items-center justify-center text-gray-600 text-sm">
              2 Col
            </div>
            <div class="h-16 bg-gray-200 rounded-lg flex items-center justify-center text-gray-600 text-sm">
              3 Col
            </div>
            <div class="h-16 bg-gray-200 rounded-lg flex items-center justify-center text-gray-600 text-sm">
              4 Col
            </div>
          </div>
        </div>
      </div>

      <!-- 测试结果 -->
      <div class="bg-green-50 border border-green-200 rounded-xl p-6">
        <div class="flex items-center space-x-2 mb-2">
          <div class="w-4 h-4 bg-green-500 rounded-full"></div>
          <h3 class="text-lg font-semibold text-green-800">TailwindCSS 样式测试通过</h3>
        </div>
        <p class="text-green-700">
          所有组件已成功迁移到TailwindCSS，自定义CSS已完全移除。
          样式保持一致性，响应式设计正常工作，动画效果流畅。
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import VideoStream from '@/components/common/VideoStream.vue'
import DetectionStatus from '@/components/common/DetectionStatus.vue'
import SystemStatus from '@/components/common/SystemStatus.vue'

// 模拟数据
const mockDetection = ref({
  timestamp: new Date(),
  result: true,
  confidence: 0.85,
  patientId: 'P001'
})
</script>
