<template>
  <div class="training-view h-screen bg-gradient-to-br from-blue-50 to-indigo-100 overflow-hidden">
    <!-- 顶部状态栏 -->
    <div class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <h1 class="text-2xl font-bold text-gray-800">智能康复训练</h1>
          <div class="flex items-center space-x-2">
            <div :class="[
              'w-3 h-3 rounded-full',
              systemStatusClass
            ]"></div>
            <span class="text-sm text-gray-600">{{ systemStatusText }}</span>
          </div>
        </div>

        <div class="flex items-center space-x-4">
          <div class="text-right">
            <div class="text-sm text-gray-500">当前用户</div>
            <div class="font-semibold text-gray-800">{{ userDisplayName }}</div>
          </div>
          <el-button
            type="danger"
            size="small"
            @click="handleExitTraining"
            :loading="isExiting"
          >
            退出训练
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="h-full grid grid-cols-12 grid-rows-6 gap-4 p-4" style="height: calc(100vh - 80px);">
      <!-- 任务列表 -->
      <div class="col-span-3 row-span-6 bg-white rounded-xl shadow-lg overflow-hidden">
        <div class="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4">
          <h3 class="text-lg font-semibold">训练任务</h3>
          <div class="text-sm opacity-90">{{ actionList.length }} 个动作</div>
        </div>

        <div class="p-4 space-y-3 max-h-full overflow-y-auto">
          <div
            v-for="(action, index) in actionList"
            :key="index"
            :class="[
              'p-3 rounded-lg border transition-all duration-200',
              isCurrentAction(action) ? 'bg-blue-50 border-blue-300 shadow-md' : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
            ]"
          >
            <div class="flex items-center justify-between">
              <div>
                <div :class="[
                  'font-medium',
                  isCurrentAction(action) ? 'text-blue-800' : 'text-gray-800'
                ]">
                  {{ getActionDisplayName(action.action_type) }}
                </div>
                <div :class="[
                  'text-sm',
                  isCurrentAction(action) ? 'text-blue-600' : 'text-gray-600'
                ]">
                  {{ action.sets }}组 x {{ action.reps_per_set }}次
                </div>
              </div>
              <div v-if="isCurrentAction(action)" class="text-blue-500">
                <el-icon size="20"><VideoPlay /></el-icon>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 用户实时画面 -->
      <div class="col-span-5 row-span-4 bg-white rounded-xl shadow-lg overflow-hidden">
        <div class="bg-gradient-to-r from-green-500 to-green-600 text-white p-4">
          <h3 class="text-lg font-semibold">实时画面</h3>
          <div class="text-sm opacity-90">姿态识别中</div>
        </div>

        <div class="p-4 h-full">
          <div class="relative w-full h-full bg-gray-900 rounded-lg overflow-hidden">
            <!-- 姿态可视化组件 -->
            <KeypointCanvas
              ref="keypointCanvasRef"
              :keypoints="mainStore.poseKeypoints"
              :width="'100%'"
              :height="'100%'"
              :show-controls="false"
              :auto-render="true"
              class="w-full h-full"
            />

            <!-- 状态覆盖层 -->
            <div v-if="showStatusOverlay" class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
              <div class="text-center text-white">
                <div class="text-xl font-semibold mb-2">{{ overlayMessage }}</div>
                <div v-if="showCountdown" class="text-4xl font-bold">{{ countdownValue }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 标准动作参考 -->
      <div class="col-span-4 row-span-4 bg-white rounded-xl shadow-lg overflow-hidden">
        <div class="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-4">
          <h3 class="text-lg font-semibold">标准动作</h3>
          <div class="text-sm opacity-90">{{ currentActionName }}</div>
        </div>

        <div class="p-4 h-full">
          <div class="w-full h-full bg-gray-100 rounded-lg flex items-center justify-center">
            <div class="text-center text-gray-500">
              <el-icon size="48" class="mb-2"><Picture /></el-icon>
              <div>标准动作参考图</div>
              <div class="text-sm mt-1">{{ currentActionDescription }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 训练进度 -->
      <div class="col-span-5 row-span-2 bg-white rounded-xl shadow-lg overflow-hidden">
        <div class="bg-gradient-to-r from-orange-500 to-orange-600 text-white p-4">
          <h3 class="text-lg font-semibold">训练进度</h3>
          <div class="text-sm opacity-90">{{ trainingPhaseText }}</div>
        </div>

        <div class="p-4">
          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-3">
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">当前组数:</span>
                <span class="font-semibold text-lg">{{ currentSet }}/{{ totalSets }}</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">当前次数:</span>
                <span class="font-semibold text-lg">{{ currentRep }}/{{ repsPerSet }}</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">总进度:</span>
                <span class="font-semibold text-lg">{{ overallProgress }}%</span>
              </div>
            </div>

            <div class="space-y-3">
              <div>
                <div class="flex justify-between text-sm mb-1">
                  <span>组进度</span>
                  <span>{{ setProgress }}%</span>
                </div>
                <el-progress
                  :percentage="setProgress"
                  :stroke-width="8"
                  color="#3b82f6"
                />
              </div>
              <div>
                <div class="flex justify-between text-sm mb-1">
                  <span>总进度</span>
                  <span>{{ overallProgress }}%</span>
                </div>
                <el-progress
                  :percentage="overallProgress"
                  :stroke-width="8"
                  color="#10b981"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 实时反馈 -->
      <div class="col-span-4 row-span-2 bg-white rounded-xl shadow-lg overflow-hidden">
        <div class="bg-gradient-to-r from-red-500 to-red-600 text-white p-4">
          <h3 class="text-lg font-semibold">实时反馈</h3>
          <div class="text-sm opacity-90">动作评分</div>
        </div>

        <div class="p-4 h-full flex items-center justify-center">
          <div class="text-center">
            <div class="relative mb-4">
              <div :class="[
                'text-6xl font-bold mb-2',
                getScoreColor(currentScore)
              ]">
                {{ currentScore }}
              </div>
              <div class="text-sm text-gray-600">当前分数</div>
            </div>

            <div class="space-y-2">
              <div class="flex items-center justify-center space-x-2">
                <el-icon :class="getScoreColor(currentScore)"><Star /></el-icon>
                <span class="text-sm font-medium">{{ getScoreLevel(currentScore) }}</span>
              </div>

              <div v-if="feedbackMessage" class="text-sm text-gray-600 max-w-xs">
                {{ feedbackMessage }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 休息时间覆盖层 -->
    <transition
      enter-active-class="transition-all duration-300 ease-out"
      leave-active-class="transition-all duration-300 ease-in"
      enter-from-class="opacity-0 scale-95"
      enter-to-class="opacity-100 scale-100"
      leave-from-class="opacity-100 scale-100"
      leave-to-class="opacity-0 scale-95"
    >
      <div v-if="showRestOverlay" class="fixed inset-0 bg-blue-500 bg-opacity-95 backdrop-blur-sm z-50 flex items-center justify-center">
        <div class="text-center text-white max-w-md">
          <div class="mb-6">
            <el-icon size="80" class="text-white opacity-80 mb-4"><Coffee /></el-icon>
            <h2 class="text-3xl font-bold mb-2">休息时间</h2>
            <p class="text-lg opacity-90">请稍作休息，准备下一组训练</p>
          </div>

          <div class="mb-6">
            <div class="text-6xl font-bold mb-2">{{ restTimeRemaining }}</div>
            <div class="text-lg">秒</div>
          </div>

          <div class="w-full max-w-xs mx-auto">
            <el-progress
              :percentage="restProgress"
              :show-text="false"
              stroke-width="6"
              color="#ffffff"
            />
          </div>
        </div>
      </div>
    </transition>

    <!-- 训练完成覆盖层 -->
    <transition
      enter-active-class="transition-all duration-500 ease-out"
      leave-active-class="transition-all duration-300 ease-in"
      enter-from-class="opacity-0 scale-75"
      enter-to-class="opacity-100 scale-100"
      leave-from-class="opacity-100 scale-100"
      leave-to-class="opacity-0 scale-110"
    >
      <div v-if="showCompletionOverlay" class="fixed inset-0 bg-green-500 bg-opacity-95 backdrop-blur-sm z-50 flex items-center justify-center">
        <div class="text-center text-white max-w-md">
          <div class="mb-6 animate-bounce">
            <el-icon size="100" class="text-white drop-shadow-lg mb-4"><CircleCheckFilled /></el-icon>
            <h2 class="text-4xl font-bold mb-2">训练完成！</h2>
            <p class="text-lg opacity-90">恭喜您完成了所有训练任务</p>
          </div>

          <div class="mb-6">
            <div class="text-2xl font-semibold mb-2">总体评分</div>
            <div class="text-5xl font-bold">{{ finalScore }}</div>
          </div>

          <div class="w-full max-w-xs mx-auto">
            <el-progress
              :percentage="completionProgress"
              :show-text="false"
              stroke-width="6"
              color="#ffffff"
            />
            <div class="text-sm mt-2 opacity-80">正在生成训练报告...</div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useMainStore } from '@/stores/main'
import KeypointCanvas from '@/components/visualization/KeypointCanvas.vue'
import {
  VideoPlay,
  Picture,
  Star,
  Coffee,
  CircleCheckFilled
} from '@element-plus/icons-vue'

// 路由和store
const router = useRouter()
const mainStore = useMainStore()

// 响应式数据
const isExiting = ref(false)
const keypointCanvasRef = ref(null)

// 状态覆盖层
const showStatusOverlay = ref(false)
const overlayMessage = ref('')
const showCountdown = ref(false)
const countdownValue = ref(3)

// 休息时间覆盖层
const showRestOverlay = ref(false)
const restTimeRemaining = ref(0)
const restProgress = ref(0)
const restTimer = ref(null)

// 训练完成覆盖层
const showCompletionOverlay = ref(false)
const completionProgress = ref(0)
const finalScore = ref(0)

// 反馈信息
const feedbackMessage = ref('')

// 计算属性
const systemStatusClass = computed(() => {
  if (!mainStore.isConnected) return 'bg-red-500'
  if (mainStore.currentState === 'ACTION_TRAINING') return 'bg-green-500'
  if (mainStore.currentState === 'ACTION_PREPARATION') return 'bg-yellow-500'
  if (mainStore.currentState === 'ACTION_REST') return 'bg-blue-500'
  return 'bg-gray-500'
})

const systemStatusText = computed(() => {
  if (!mainStore.isConnected) return '系统未连接'

  const stateTextMap = {
    'ACTION_PREPARATION': '准备训练',
    'ACTION_TRAINING': '训练中',
    'ACTION_REST': '休息中',
    'ACTION_COMPLETED': '训练完成'
  }

  return stateTextMap[mainStore.currentState] || '未知状态'
})

const userDisplayName = computed(() => {
  return mainStore.userInfo?.name || mainStore.userInfo?.patient_id || '未知用户'
})

const actionList = computed(() => {
  return mainStore.actionList || []
})

const currentActionName = computed(() => {
  return mainStore.currentAction?.action_type || '无'
})

const currentActionDescription = computed(() => {
  const actionDescriptions = {
    'shoulder_touch': '左手摸右肩膀',
    'arm_raise': '双臂上举',
    'finger_touch': '对指练习',
    'palm_flip': '手掌翻转'
  }
  return actionDescriptions[currentActionName.value] || '标准动作演示'
})

const currentSet = computed(() => {
  return mainStore.currentAction?.current_set || 0
})

const totalSets = computed(() => {
  return mainStore.currentAction?.sets || 0
})

const currentRep = computed(() => {
  return mainStore.currentAction?.current_rep || 0
})

const repsPerSet = computed(() => {
  return mainStore.currentAction?.reps_per_set || 0
})

const currentScore = computed(() => {
  return mainStore.currentAction?.current_score || 0
})

const trainingPhaseText = computed(() => {
  if (mainStore.currentState === 'ACTION_PREPARATION') return '准备阶段'
  if (mainStore.currentState === 'ACTION_TRAINING') return '训练阶段'
  if (mainStore.currentState === 'ACTION_REST') return '休息阶段'
  return '等待开始'
})

const setProgress = computed(() => {
  if (repsPerSet.value === 0) return 0
  return Math.round((currentRep.value / repsPerSet.value) * 100)
})

const overallProgress = computed(() => {
  if (totalSets.value === 0 || repsPerSet.value === 0) return 0
  const totalReps = totalSets.value * repsPerSet.value
  const completedReps = (currentSet.value - 1) * repsPerSet.value + currentRep.value
  return Math.round((completedReps / totalReps) * 100)
})

// 工具方法
const isCurrentAction = (action) => {
  return action.action_type === currentActionName.value
}

const getActionDisplayName = (actionType) => {
  const actionNames = {
    'shoulder_touch': '左手摸右肩',
    'arm_raise': '双臂上举',
    'finger_touch': '对指练习',
    'palm_flip': '手掌翻转'
  }
  return actionNames[actionType] || actionType
}

const getScoreColor = (score) => {
  if (score >= 90) return 'text-green-500'
  if (score >= 80) return 'text-blue-500'
  if (score >= 70) return 'text-yellow-500'
  if (score >= 60) return 'text-orange-500'
  return 'text-red-500'
}

const getScoreLevel = (score) => {
  if (score >= 90) return '优秀'
  if (score >= 80) return '良好'
  if (score >= 70) return '一般'
  if (score >= 60) return '需改进'
  return '需努力'
}

// 倒计时相关方法
const startCountdown = (seconds = 3, message = '准备开始') => {
  overlayMessage.value = message
  countdownValue.value = seconds
  showStatusOverlay.value = true
  showCountdown.value = true

  const timer = setInterval(() => {
    countdownValue.value--
    if (countdownValue.value <= 0) {
      clearInterval(timer)
      showStatusOverlay.value = false
      showCountdown.value = false
    }
  }, 1000)
}

// 休息时间处理
const startRestTime = (duration) => {
  console.log('开始休息时间:', duration)
  restTimeRemaining.value = duration
  restProgress.value = 0
  showRestOverlay.value = true

  const startTime = Date.now()
  const totalDuration = duration * 1000

  restTimer.value = setInterval(() => {
    const elapsed = Date.now() - startTime
    const remaining = Math.max(0, totalDuration - elapsed)

    restTimeRemaining.value = Math.ceil(remaining / 1000)
    restProgress.value = Math.round(((totalDuration - remaining) / totalDuration) * 100)

    if (remaining <= 0) {
      clearInterval(restTimer.value)
      showRestOverlay.value = false
      ElMessage.success('休息结束，继续训练！')
    }
  }, 100)
}

// 训练完成处理
const showTrainingCompletion = (score) => {
  console.log('显示训练完成:', score)
  finalScore.value = score
  completionProgress.value = 0
  showCompletionOverlay.value = true

  // 进度条动画
  const progressTimer = setInterval(() => {
    completionProgress.value += 2
    if (completionProgress.value >= 100) {
      clearInterval(progressTimer)
    }
  }, 50)
}

// 退出训练
const handleExitTraining = async () => {
  try {
    const result = await ElMessageBox.confirm(
      '确定要退出训练吗？当前进度将会丢失。',
      '退出训练',
      {
        confirmButtonText: '确定退出',
        cancelButtonText: '继续训练',
        type: 'warning'
      }
    )

    if (result === 'confirm') {
      isExiting.value = true
      // 清理定时器
      if (restTimer.value) {
        clearInterval(restTimer.value)
      }

      // 跳转回登录页面
      setTimeout(() => {
        router.push('/login')
      }, 1000)
    }
  } catch (error) {
    // 用户取消退出
    console.log('用户取消退出训练')
  }
}

// 监听器
watch(() => mainStore.currentState, (newState, oldState) => {
  console.log('训练状态变化:', oldState, '->', newState)

  if (newState === 'ACTION_PREPARATION' && oldState !== 'ACTION_PREPARATION') {
    // 进入准备阶段
    overlayMessage.value = '准备开始训练'
    showStatusOverlay.value = true
    showCountdown.value = false
  } else if (newState === 'ACTION_TRAINING') {
    // 进入训练阶段
    showStatusOverlay.value = false
  } else if (newState === 'ACTION_REST') {
    // 进入休息阶段
    const restTime = mainStore.currentAction?.rest_time || 30
    startRestTime(restTime)
  } else if (newState === 'ACTION_COMPLETED') {
    // 训练完成
    const score = mainStore.currentAction?.average_score || 0
    showTrainingCompletion(score)
  }
})

// 监听动作准备就绪消息
watch(() => mainStore.message, (newMessage) => {
  if (newMessage && newMessage.includes('准备完成')) {
    startCountdown(3, '准备完成！即将开始训练')
  }
})

// 监听训练信息更新
watch(() => mainStore.currentAction, (newAction) => {
  if (newAction && newAction.feedback) {
    feedbackMessage.value = newAction.feedback
  }
}, { deep: true })

// 监听训练完成
watch(() => mainStore.trainingSession, (newSession) => {
  if (newSession) {
    console.log('训练会话完成:', newSession)
    // 主store已经处理了自动跳转到报告页面
  }
})

// 生命周期
onMounted(() => {
  console.log('训练页面加载完成')

  // 检查用户是否已登录
  if (!mainStore.isUserLoggedIn) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }

  // 检查是否有训练任务
  if (!mainStore.actionList || mainStore.actionList.length === 0) {
    ElMessage.warning('没有可用的训练任务')
    router.push('/login')
    return
  }

  console.log('训练页面初始化完成，当前状态:', mainStore.currentState)
})

onUnmounted(() => {
  // 清理定时器
  if (restTimer.value) {
    clearInterval(restTimer.value)
  }

  console.log('训练页面卸载')
})
</script>
<style scoped>
.training-view {
  /* 确保固定100vh高度 */
  height: 100vh;
  overflow: hidden;
}
</style>
