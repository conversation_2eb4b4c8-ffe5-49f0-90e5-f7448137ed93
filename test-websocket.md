# WebSocket 连接测试指南

## 测试方案

我已经为您创建了两种测试WebSocket连接的方案：

### 方案1：简单HTML测试页面（推荐快速测试）

**文件位置**: `frontend/test-websocket.html`

**使用方法**:
1. 直接在浏览器中打开 `frontend/test-websocket.html` 文件
2. 确保后端服务器运行在 `http://localhost:5000`
3. 点击"连接"按钮测试连接
4. 使用各种按钮发送测试消息

**功能特性**:
- ✅ 实时连接状态显示
- ✅ 消息发送和接收
- ✅ 统计信息显示
- ✅ 消息历史记录
- ✅ 自定义消息发送
- ✅ 系统重置测试

### 方案2：Vue组件测试（完整前端环境）

**文件位置**: 
- `frontend/src/services/websocket.js` - WebSocket服务
- `frontend/src/components/WebSocketTest.vue` - 测试组件
- `frontend/src/router/index.js` - 路由配置

**使用方法**:
1. 进入frontend目录: `cd frontend`
2. 安装依赖: `npm install`
3. 启动开发服务器: `npm run dev`
4. 在浏览器中访问 `http://localhost:5173`

## 后端启动步骤

在测试前端之前，请确保后端正常运行：

```bash
# 进入后端目录
cd backend

# 激活虚拟环境
venv\Scripts\activate

# 启动后端服务
python system_launcher.py
```

## 测试步骤

### 1. 基础连接测试
- [ ] 启动后端服务
- [ ] 打开测试页面
- [ ] 点击"连接"按钮
- [ ] 观察连接状态变为"已连接"
- [ ] 检查控制台是否有连接成功的日志

### 2. 消息发送测试
- [ ] 点击"发送系统重置"按钮
- [ ] 观察后端控制台是否收到消息
- [ ] 检查前端是否收到响应消息
- [ ] 尝试发送自定义消息

### 3. 数据接收测试
- [ ] 观察是否能收到后端主动发送的消息
- [ ] 检查姿态数据是否正常接收
- [ ] 验证消息格式是否正确

### 4. 断线重连测试
- [ ] 手动断开连接
- [ ] 重新连接
- [ ] 测试自动重连功能

## 预期结果

### 成功连接的标志：
1. **连接状态**: 显示为"已连接"，绿色指示灯
2. **Socket ID**: 显示有效的Socket ID
3. **后端日志**: 显示"前端客户端已连接"
4. **消息接收**: 能够接收到后端发送的各种消息类型

### 常见消息类型：
- `system_state` - 系统状态消息
- `user_detected` - 用户检测消息  
- `pose_data` - 姿态数据消息
- `action_recognized` - 动作识别消息
- `training_progress` - 训练进度消息
- `error_occurred` - 错误消息

## 故障排除

### 连接失败
1. 检查后端是否正常启动
2. 确认端口5000没有被占用
3. 检查防火墙设置
4. 查看浏览器控制台错误信息

### 消息接收异常
1. 检查后端WebSocketManager是否正常工作
2. 确认消息格式是否正确
3. 查看后端日志中的错误信息

### 性能问题
1. 观察消息延迟是否在50ms以内
2. 检查消息队列是否有积压
3. 监控CPU和内存使用情况

## 测试数据示例

### 系统重置消息
```json
{
  "timestamp": "2024-01-20T10:30:00.000Z",
  "source": "websocket_test_page"
}
```

### 自定义测试消息
```json
{
  "message": "Hello from frontend!",
  "timestamp": "2024-01-20T10:30:00.000Z",
  "source": "websocket_test_page"
}
```

## 成功标准

- ✅ 连接建立成功，无错误
- ✅ 消息发送和接收正常
- ✅ 消息延迟小于50ms
- ✅ 断线重连功能正常
- ✅ 后端日志显示正确的消息处理
- ✅ 前端能够正确解析和显示消息

完成测试后，请报告测试结果，特别是：
1. 连接是否成功建立
2. 是否能正常收发消息
3. 消息延迟情况
4. 是否有任何错误或异常
