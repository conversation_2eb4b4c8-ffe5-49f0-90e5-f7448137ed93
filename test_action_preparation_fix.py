#!/usr/bin/env python3
"""
测试ACTION_PREPARATION状态处理器的修复
验证变量作用域问题是否已解决
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.services.business.state_handlers.action_preparation_handler import ActionPreparationHandler

def test_posture_feedback_fix():
    """测试姿态反馈处理的修复"""
    print("开始测试ACTION_PREPARATION状态处理器修复...")
    
    # 创建处理器实例
    handler = ActionPreparationHandler()
    
    # 测试场景1：检测到坐姿的情况
    print("\n测试场景1：检测到坐姿")
    posture_feedback_sitting = {
        'progress': 0.5,
        'sitting_duration': 1.5,
        'required_duration': 3.0
    }
    
    try:
        result1 = handler._continue_preparation_with_feedback(True, posture_feedback_sitting)
        print("✅ 检测到坐姿的情况处理成功")
        print(f"   消息: {result1['state_data'].message}")
        print(f"   进度: {result1['state_data'].progress_info['preparation_progress']}")
    except Exception as e:
        print(f"❌ 检测到坐姿的情况处理失败: {e}")
    
    # 测试场景2：未检测到坐姿的情况（之前会出错的场景）
    print("\n测试场景2：未检测到坐姿")
    posture_feedback_not_sitting = {
        'progress': 0.0,
        'sitting_duration': 0.0,
        'required_duration': 3.0
    }
    
    try:
        result2 = handler._continue_preparation_with_feedback(False, posture_feedback_not_sitting)
        print("✅ 未检测到坐姿的情况处理成功")
        print(f"   消息: {result2['state_data'].message}")
        print(f"   进度: {result2['state_data'].progress_info['preparation_progress']}")
    except Exception as e:
        print(f"❌ 未检测到坐姿的情况处理失败: {e}")
    
    # 测试场景3：空的姿态反馈数据
    print("\n测试场景3：空的姿态反馈数据")
    posture_feedback_empty = {}
    
    try:
        result3 = handler._continue_preparation_with_feedback(False, posture_feedback_empty)
        print("✅ 空姿态反馈数据处理成功")
        print(f"   消息: {result3['state_data'].message}")
        print(f"   进度: {result3['state_data'].progress_info['preparation_progress']}")
    except Exception as e:
        print(f"❌ 空姿态反馈数据处理失败: {e}")
    
    print("\n测试完成！")

if __name__ == "__main__":
    test_posture_feedback_fix()
